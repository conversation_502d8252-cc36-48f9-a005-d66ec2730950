{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\ActivityTimeline\\\\Overview\\\\Overview.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Button } from '@mui/material';\nimport MonthlyWorkReport from './component/MonthlyWorkReport';\nimport DayWorkReport from './component/DayWorkReport';\nimport WeekWorkReport from './component/WeekWorkReport';\nimport DayPicker from './TimePickers/DayPicker';\nimport WeeklyPicker from './TimePickers/WeeklyPicker';\nimport MonthPicker from './TimePickers/MonthPicker';\nimport { useDispatch } from 'react-redux';\nimport { ActivityActions } from '../../../slices/actions';\nimport dayjs from 'dayjs';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction Overview() {\n  _s();\n  const dispatch = useDispatch();\n  const [viewOption, setViewOption] = useState('Day');\n\n  // Set default date range based on view option\n  const getDefaultDateRange = view => {\n    const today = dayjs();\n    switch (view) {\n      case 'Day':\n        return {\n          startDate: today.format('YYYY-MM-DD'),\n          endDate: today.format('YYYY-MM-DD')\n        };\n      case 'Week':\n        return {\n          startDate: today.startOf('week').format('YYYY-MM-DD'),\n          endDate: today.endOf('week').format('YYYY-MM-DD')\n        };\n      case 'Month':\n        return {\n          startDate: today.startOf('month').format('YYYY-MM-DD'),\n          endDate: today.endOf('month').format('YYYY-MM-DD')\n        };\n      default:\n        return {\n          startDate: today.format('YYYY-MM-DD'),\n          endDate: today.format('YYYY-MM-DD')\n        };\n    }\n  };\n  const [dateRange, setDateRange] = useState(getDefaultDateRange('Day'));\n\n  // Handle date changes from pickers\n  const handleDateChange = newDateRange => {\n    setDateRange(newDateRange);\n  };\n\n  // Fetch activity data when date range or view changes\n  useEffect(() => {\n    if (dateRange.startDate && dateRange.endDate) {\n      dispatch(ActivityActions.getUserActivity({\n        startDate: dateRange.startDate,\n        endDate: dateRange.endDate,\n        view: viewOption.toLowerCase()\n      }));\n    }\n  }, [dateRange, viewOption, dispatch]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Overview\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3,\n        position: 'relative'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          borderRadius: '4px',\n          overflow: 'hidden',\n          border: '1px solid #e0e0e0'\n        },\n        children: ['Day', 'Week', 'Month'].map(option => /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setViewOption(option),\n          sx: {\n            bgcolor: viewOption === option ? 'primary.main' : 'transparent',\n            color: viewOption === option ? 'white' : 'text.primary',\n            borderRadius: 0,\n            '&:hover': {\n              bgcolor: viewOption === option ? 'primary.dark' : 'rgba(0, 0, 0, 0.04)'\n            }\n          },\n          children: option\n        }, option, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), viewOption === 'Day' && /*#__PURE__*/_jsxDEV(DayPicker, {\n        onChange: handleDateChange\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 34\n      }, this), viewOption === 'Week' && /*#__PURE__*/_jsxDEV(WeeklyPicker, {\n        onChange: handleDateChange\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 35\n      }, this), viewOption === 'Month' && /*#__PURE__*/_jsxDEV(MonthPicker, {\n        onChange: handleDateChange\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 36\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), viewOption === 'Day' && /*#__PURE__*/_jsxDEV(DayWorkReport, {\n      dateRange: dateRange\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 32\n    }, this), viewOption === 'Week' && /*#__PURE__*/_jsxDEV(WeekWorkReport, {\n      dateRange: dateRange\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 33\n    }, this), viewOption === 'Month' && /*#__PURE__*/_jsxDEV(MonthlyWorkReport, {\n      dateRange: dateRange\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 34\n    }, this)]\n  }, void 0, true);\n}\n_s(Overview, \"Fp1x4/DfqJLSMfch52W2y2x0U1U=\", false, function () {\n  return [useDispatch];\n});\n_c = Overview;\nexport default Overview;\nvar _c;\n$RefreshReg$(_c, \"Overview\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "<PERSON><PERSON>", "MonthlyWorkReport", "DayWorkReport", "WeekWorkReport", "DayPicker", "WeeklyPicker", "MonthPicker", "useDispatch", "ActivityActions", "dayjs", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Overview", "_s", "dispatch", "viewOption", "setViewOption", "getDefaultDateRange", "view", "today", "startDate", "format", "endDate", "startOf", "endOf", "date<PERSON><PERSON><PERSON>", "setDateRange", "handleDateChange", "newDateRange", "getUserActivity", "toLowerCase", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "display", "justifyContent", "alignItems", "mb", "position", "borderRadius", "overflow", "border", "map", "option", "onClick", "bgcolor", "color", "onChange", "_c", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/ActivityTimeline/Overview/Overview.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { Box, Button } from '@mui/material';\r\nimport MonthlyWorkReport from './component/MonthlyWorkReport';\r\nimport DayWorkReport from './component/DayWorkReport';\r\nimport WeekWorkReport from './component/WeekWorkReport';\r\nimport DayPicker from './TimePickers/DayPicker';\r\nimport WeeklyPicker from './TimePickers/WeeklyPicker';\r\nimport MonthPicker from './TimePickers/MonthPicker';\r\nimport { useDispatch } from 'react-redux';\r\nimport { ActivityActions } from '../../../slices/actions';\r\nimport dayjs from 'dayjs';\r\n\r\nfunction Overview() {\r\n  const dispatch = useDispatch();\r\n  const [viewOption, setViewOption] = useState('Day');\r\n\r\n  // Set default date range based on view option\r\n  const getDefaultDateRange = (view) => {\r\n    const today = dayjs();\r\n    switch(view) {\r\n      case 'Day':\r\n        return {\r\n          startDate: today.format('YYYY-MM-DD'),\r\n          endDate: today.format('YYYY-MM-DD')\r\n        };\r\n      case 'Week':\r\n        return {\r\n          startDate: today.startOf('week').format('YYYY-MM-DD'),\r\n          endDate: today.endOf('week').format('YYYY-MM-DD')\r\n        };\r\n      case 'Month':\r\n        return {\r\n          startDate: today.startOf('month').format('YYYY-MM-DD'),\r\n          endDate: today.endOf('month').format('YYYY-MM-DD')\r\n        };\r\n      default:\r\n        return {\r\n          startDate: today.format('YYYY-MM-DD'),\r\n          endDate: today.format('YYYY-MM-DD')\r\n        };\r\n    }\r\n  };\r\n\r\n  const [dateRange, setDateRange] = useState(getDefaultDateRange('Day'));\r\n\r\n  // Handle date changes from pickers\r\n  const handleDateChange = (newDateRange) => {\r\n    setDateRange(newDateRange);\r\n  };\r\n\r\n  // Fetch activity data when date range or view changes\r\n  useEffect(() => {\r\n    if (dateRange.startDate && dateRange.endDate) {\r\n      dispatch(ActivityActions.getUserActivity({\r\n        startDate: dateRange.startDate,\r\n        endDate: dateRange.endDate,\r\n        view: viewOption.toLowerCase()\r\n      }));\r\n    }\r\n  }, [dateRange, viewOption, dispatch]);\r\n\r\n  return (\r\n    <>\r\n      <h1>Overview</h1>\r\n\r\n      {/* View options and date picker */}\r\n      <Box\r\n        sx={{\r\n          display: 'flex',\r\n          justifyContent: 'space-between',\r\n          alignItems: 'center',\r\n          mb: 3,\r\n          position: 'relative',\r\n        }}\r\n      >\r\n        {/* Day/Week/Month tabs */}\r\n        <Box\r\n          sx={{\r\n            display: 'flex',\r\n            borderRadius: '4px',\r\n            overflow: 'hidden',\r\n            border: '1px solid #e0e0e0',\r\n          }}\r\n        >\r\n          {['Day', 'Week', 'Month'].map((option) => (\r\n            <Button\r\n              key={option}\r\n              onClick={() => setViewOption(option)}\r\n              sx={{\r\n                bgcolor: viewOption === option ? 'primary.main' : 'transparent',\r\n                color: viewOption === option ? 'white' : 'text.primary',\r\n                borderRadius: 0,\r\n                '&:hover': {\r\n                  bgcolor: viewOption === option ? 'primary.dark' : 'rgba(0, 0, 0, 0.04)',\r\n                },\r\n              }}\r\n            >\r\n              {option}\r\n            </Button>\r\n          ))}\r\n        </Box>\r\n\r\n        {/* Date Pickers */}\r\n        {viewOption === 'Day' && <DayPicker onChange={handleDateChange} />}\r\n        {viewOption === 'Week' && <WeeklyPicker onChange={handleDateChange} />}\r\n        {viewOption === 'Month' && <MonthPicker onChange={handleDateChange} />}\r\n      </Box>\r\n\r\n      {/* Conditionally render view */}\r\n      {viewOption === 'Day' && <DayWorkReport dateRange={dateRange} />}\r\n      {viewOption === 'Week' && <WeekWorkReport dateRange={dateRange} />}\r\n      {viewOption === 'Month' && <MonthlyWorkReport dateRange={dateRange} />}\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Overview;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,MAAM,QAAQ,eAAe;AAC3C,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,SAASC,QAAQA,CAAA,EAAG;EAAAC,EAAA;EAClB,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAMsB,mBAAmB,GAAIC,IAAI,IAAK;IACpC,MAAMC,KAAK,GAAGZ,KAAK,CAAC,CAAC;IACrB,QAAOW,IAAI;MACT,KAAK,KAAK;QACR,OAAO;UACLE,SAAS,EAAED,KAAK,CAACE,MAAM,CAAC,YAAY,CAAC;UACrCC,OAAO,EAAEH,KAAK,CAACE,MAAM,CAAC,YAAY;QACpC,CAAC;MACH,KAAK,MAAM;QACT,OAAO;UACLD,SAAS,EAAED,KAAK,CAACI,OAAO,CAAC,MAAM,CAAC,CAACF,MAAM,CAAC,YAAY,CAAC;UACrDC,OAAO,EAAEH,KAAK,CAACK,KAAK,CAAC,MAAM,CAAC,CAACH,MAAM,CAAC,YAAY;QAClD,CAAC;MACH,KAAK,OAAO;QACV,OAAO;UACLD,SAAS,EAAED,KAAK,CAACI,OAAO,CAAC,OAAO,CAAC,CAACF,MAAM,CAAC,YAAY,CAAC;UACtDC,OAAO,EAAEH,KAAK,CAACK,KAAK,CAAC,OAAO,CAAC,CAACH,MAAM,CAAC,YAAY;QACnD,CAAC;MACH;QACE,OAAO;UACLD,SAAS,EAAED,KAAK,CAACE,MAAM,CAAC,YAAY,CAAC;UACrCC,OAAO,EAAEH,KAAK,CAACE,MAAM,CAAC,YAAY;QACpC,CAAC;IACL;EACF,CAAC;EAED,MAAM,CAACI,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAACsB,mBAAmB,CAAC,KAAK,CAAC,CAAC;;EAEtE;EACA,MAAMU,gBAAgB,GAAIC,YAAY,IAAK;IACzCF,YAAY,CAACE,YAAY,CAAC;EAC5B,CAAC;;EAED;EACAhC,SAAS,CAAC,MAAM;IACd,IAAI6B,SAAS,CAACL,SAAS,IAAIK,SAAS,CAACH,OAAO,EAAE;MAC5CR,QAAQ,CAACR,eAAe,CAACuB,eAAe,CAAC;QACvCT,SAAS,EAAEK,SAAS,CAACL,SAAS;QAC9BE,OAAO,EAAEG,SAAS,CAACH,OAAO;QAC1BJ,IAAI,EAAEH,UAAU,CAACe,WAAW,CAAC;MAC/B,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACL,SAAS,EAAEV,UAAU,EAAED,QAAQ,CAAC,CAAC;EAErC,oBACEL,OAAA,CAAAE,SAAA;IAAAoB,QAAA,gBACEtB,OAAA;MAAAsB,QAAA,EAAI;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAGjB1B,OAAA,CAACZ,GAAG;MACFuC,EAAE,EAAE;QACFC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBC,EAAE,EAAE,CAAC;QACLC,QAAQ,EAAE;MACZ,CAAE;MAAAV,QAAA,gBAGFtB,OAAA,CAACZ,GAAG;QACFuC,EAAE,EAAE;UACFC,OAAO,EAAE,MAAM;UACfK,YAAY,EAAE,KAAK;UACnBC,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAE;QACV,CAAE;QAAAb,QAAA,EAED,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAACc,GAAG,CAAEC,MAAM,iBACnCrC,OAAA,CAACX,MAAM;UAELiD,OAAO,EAAEA,CAAA,KAAM/B,aAAa,CAAC8B,MAAM,CAAE;UACrCV,EAAE,EAAE;YACFY,OAAO,EAAEjC,UAAU,KAAK+B,MAAM,GAAG,cAAc,GAAG,aAAa;YAC/DG,KAAK,EAAElC,UAAU,KAAK+B,MAAM,GAAG,OAAO,GAAG,cAAc;YACvDJ,YAAY,EAAE,CAAC;YACf,SAAS,EAAE;cACTM,OAAO,EAAEjC,UAAU,KAAK+B,MAAM,GAAG,cAAc,GAAG;YACpD;UACF,CAAE;UAAAf,QAAA,EAEDe;QAAM,GAXFA,MAAM;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAYL,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAGLpB,UAAU,KAAK,KAAK,iBAAIN,OAAA,CAACP,SAAS;QAACgD,QAAQ,EAAEvB;MAAiB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACjEpB,UAAU,KAAK,MAAM,iBAAIN,OAAA,CAACN,YAAY;QAAC+C,QAAQ,EAAEvB;MAAiB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACrEpB,UAAU,KAAK,OAAO,iBAAIN,OAAA,CAACL,WAAW;QAAC8C,QAAQ,EAAEvB;MAAiB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnE,CAAC,EAGLpB,UAAU,KAAK,KAAK,iBAAIN,OAAA,CAACT,aAAa;MAACyB,SAAS,EAAEA;IAAU;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAC/DpB,UAAU,KAAK,MAAM,iBAAIN,OAAA,CAACR,cAAc;MAACwB,SAAS,EAAEA;IAAU;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACjEpB,UAAU,KAAK,OAAO,iBAAIN,OAAA,CAACV,iBAAiB;MAAC0B,SAAS,EAAEA;IAAU;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eACtE,CAAC;AAEP;AAACtB,EAAA,CAtGQD,QAAQ;EAAA,QACEP,WAAW;AAAA;AAAA8C,EAAA,GADrBvC,QAAQ;AAwGjB,eAAeA,QAAQ;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}