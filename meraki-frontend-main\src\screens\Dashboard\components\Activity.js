/**
 * Activity Component - User's daily activity tracking interface
 */

import React, { useEffect, useState, createContext } from "react";
import Can from "../../../utils/can";
import { actions, features } from "../../../constants/permission";
import {
  Box,
  Button,
  Card,
  Grid,
  Typography,
  MenuItem,
  Select,
  FormControl,
  CircularProgress,
} from "@mui/material";
import { Timer, AssignmentIndOutlined } from "@mui/icons-material";
import moment from "moment";
import {
  ActivityActions,
  AttendanceActions,
  GeneralActions,
} from "../../../slices/actions";
import { useDispatch, useSelector } from "react-redux";
import { AttendanceSelector, UserSelector } from "../../../selectors";
import { ActivitySelector } from "selectors/ActivitySelector";
import { LeaveSelector } from "selectors/LeaveSelector";
import { SettingSelector } from "selectors/SettingSelector";
import { styled, useTheme } from "@mui/material/styles";
import TodayGoal from "./TodayGoal";
import OtherBreak from "./BreakReasone";
import EarlyLate from "./EarlyLate";
import OverLimitBreak from "./OverLimitBreak";
import WorkHoursStatus from "./WorkHoursStatus";
import ProductivityChart from "./ProductivityChart";
// import TaskProgressBar from "./TaskProgressBar";
import AttendanceBarChart from "./AttendanceBarChart";
import Widget from "./Widget";

// Context for sharing activity data with child components
export const activityContext = createContext();

const InfoLog = styled(Box)(() => ({
  display: "flex",
  justifyContent: "space-between",
}));

export default function Activity() {
  const theme = useTheme();
  const dispatch = useDispatch();
  const profile = useSelector(UserSelector.profile());
  const attendances = useSelector(AttendanceSelector.getAttendances());
  const activities = useSelector(ActivitySelector.getActivityHistory());
  const countLeave = useSelector(LeaveSelector.countLeave());
  const setting = useSelector(SettingSelector.getSetting());
  
  // State variables
  const [controller, setController] = useState(false);
  const [lunchController, setLunch] = useState(false);
  const [lunchType, setLunchType] = useState(localStorage.getItem("lunchType"));
  const [showBreakPop, setShowBreakPop] = useState(false);
  const [lateCheckIn, setLateCheckIn] = useState(false);
  const [earlyCheckOut, setEarlyCheckOut] = useState(false);
  const [todayStatus, setTodayStatus] = useState(false);
  const [overLimitBreak, setOverLimitBreak] = useState(false);
  const [slotController, setSlotController] = useState(true);
  const [todayActivity, setActivity] = useState([]);
  const [attendance, setAttendance] = useState({});
  const [showGoalPopup, setShowGoalPopup] = useState(false);
  const [workHoursStatus, setWorkHoursStatus] = useState(null);
  const [showWorkHoursStatus, setShowWorkHoursStatus] = useState(false);
  const [checkingIn, setCheckingIn] = useState(false);
  const [checkingOut, setCheckingOut] = useState(false);



  // Handle break actions based on lunch type selection
  useEffect(() => {
    if (todayActivity.length > 0 && todayActivity[0]?._id && profile && profile._id && attendance && attendance._id) {
      switch (lunchType) {
        case "lunchBreak":
          setLunch(!lunchController);
          dispatch(
            AttendanceActions.createLunchBreak({
              id: attendance._id,
              lunchIn: new Date(),
            })
          );
          dispatch(
            ActivityActions.breakStartRed({
              _id: todayActivity[0]._id,
              type: lunchType,
              breakStart: new Date().setMilliseconds(0),
              description: "Lunch Break",
              user: profile._id,
            })
          );
          setSlotController(false);
          break;

        case "teaBreak":
          setLunch(!lunchController);
          dispatch(
            AttendanceActions.createLunchBreak({
              id: attendance._id,
              lunchIn: new Date(),
            })
          );
          dispatch(
            ActivityActions.breakStartRed({
              _id: todayActivity[0]._id,
              breakStart: new Date().setMilliseconds(0),
              type: lunchType,
              description: "Tea Break",
              user: profile._id,
            })
          );
          setSlotController(false);
          break;

        case "other":
          setLunch(!lunchController);
          dispatch(
            AttendanceActions.createLunchBreak({
              id: attendance._id,
              lunchIn: new Date().setMilliseconds(0),
              user: profile._id,
            })
          );
          setShowBreakPop(true);
          setSlotController(false);
          break;

        case "breakOut":
          setLunch(!lunchController);
          dispatch(
            AttendanceActions.updateLunchBreak({
              id: attendance._id,
              lunchOut: new Date(),
            })
          );
          dispatch(
            ActivityActions.breakEndRed({
              _id: todayActivity[0]._id,
              breakEnd: new Date().setMilliseconds(0),
              type: lunchType,
              user: profile._id,
            })
          );
          setSlotController(true);
          break;

        default:
          break;
      }
    }
  }, [lunchType]);

  // Process activity data to find today's activity
  useEffect(() => {
    if (activities && activities.length > 0) {
      // Get today's date for comparison
      const today = new Date();
      const todayYear = today.getFullYear();
      const todayMonth = today.getMonth();
      const todayDate = today.getDate();
      
      // Find today's activity by comparing full date (year, month, day)
      const todayActivity = activities.find(activity => {
        if (!activity.checkInTime) { return false }
        
        const activityDate = new Date(activity.checkInTime);
        return (
          activityDate.getFullYear() === todayYear &&
          activityDate.getMonth() === todayMonth &&
          activityDate.getDate() === todayDate
        );
      });
      
      if (todayActivity) {
        setActivity([todayActivity]);
        setShowGoalPopup(false);
        sessionStorage.setItem('hasShownGoalPopup', 'true');
      } else {
        // No activity found for today
        setActivity([]);
      }
    }
  }, [activities]);

  // Handle popup displays for various activity statuses
  useEffect(() => {
    if (todayActivity.length > 0) {
      // Handle late check-in popup
      if (
        todayActivity[0].lateCheckInStatus &&
        !todayActivity.some((obj) =>
          Object.prototype.hasOwnProperty.call(obj, "lateCheckInDiscription")
        )
      ) {
        setLateCheckIn(true);
        // sessionStorage.setItem('hasShownLateCheckInPopup', 'true');
      }

      // Handle early check-out popup
      if (
        todayActivity[0].earlyCheckOutStatus &&
        !todayActivity.some((obj) =>
          Object.prototype.hasOwnProperty.call(obj, "earlyCheckOutDiscription")
        )
      ) {
        setEarlyCheckOut(true);
      }

      // Handle today's status popup (after checkout)
      if (
        todayActivity.some((obj) =>
          Object.prototype.hasOwnProperty.call(obj, "checkOutTime")
        ) &&
        !todayActivity.some((obj) =>
          Object.prototype.hasOwnProperty.call(obj, "workStatus")
        ) 
      ) {
        setTodayStatus(true);
      }

      // Handle over-limit break popup
      if (
        todayActivity[0].breaksHistory &&
        todayActivity[0].overLimitBreakStatus === true
      ) {
        setOverLimitBreak(true);
        // sessionStorage.setItem('hasShownOverLimitBreakPopup', 'true');
      }
    }
  }, [todayActivity]);

  // Fetch initial attendance and activity data
  useEffect(() => {
    if (Can(actions.read, features.attendance) && profile && profile._id) {
      // Always get the current date to ensure we're getting today's data
      const today = new Date();
      const formattedDate = today.toISOString().split('T')[0];
      
      // Clear session storage flags at the start of a new day
      const lastLoginDate = localStorage.getItem('lastLoginDate');
      if (lastLoginDate !== formattedDate) {
        sessionStorage.removeItem('hasShownGoalPopup');
        sessionStorage.removeItem('hasShownLateCheckInPopup');
        sessionStorage.removeItem('hasShownEarlyCheckOutPopup');
        sessionStorage.removeItem('hasShownTodayStatusPopup');
        sessionStorage.removeItem('hasShownOverLimitBreakPopup');
        localStorage.setItem('lastLoginDate', formattedDate);
      }
      
      dispatch(
        AttendanceActions.getAttendances({
          user: profile._id,
          date: formattedDate,
        })
      );
      dispatch(
        ActivityActions.getUserActivity({
          id: profile._id,
        })
      );
    }
  }, [profile, dispatch]);

  // Update local attendance state when attendance data changes
  useEffect(() => {
    // Check if the attendance data is for today
    const today = new Date();
    const todayDate = today.toISOString().split('T')[0];
    
    if (attendances.length > 0) {
      // Filter for today's attendance only
      const todayAttendances = attendances.filter(att => {
        const attDate = new Date(att.checkIn).toISOString().split('T')[0];
        return attDate === todayDate;
      });
      
      if (todayAttendances.length > 0) {
        setAttendance(todayAttendances[0]);
      } else {
        // No attendance for today
        setAttendance({});
      }
    } else {
      setAttendance({});
    }
  }, [attendances]);

  // Refresh attendance data after check-in or break actions
  useEffect(() => {
    if (Can(actions.read, features.attendance) && profile && profile._id) {
      // Always get a fresh date object to ensure we're getting today's data
      const today = new Date();
      const formattedDate = today.toISOString().split('T')[0];
      
      // Force cache refresh by adding a timestamp
      const timestamp = Date.now();
      
      dispatch(
        AttendanceActions.getAttendances({
          user: profile._id,
          date: formattedDate,
          _t: timestamp, // Add timestamp to prevent caching
        })
      );
    }
    dispatch(
      GeneralActions.removeSuccess(AttendanceActions.createAttendance.type)
    );
  }, [controller, lunchController, profile, dispatch]);

  // Persist lunch type to localStorage
  useEffect(() => {
    localStorage.setItem("lunchType", lunchType);
  }, [lunchType]);

  // Handle user check-in action
  const handleCheckIn = () => {
  if (checkingIn) { 
    return; 
  }
  
  console.log("check in clicked");
  
  // Clear any existing attendance data to ensure fresh check-in
  setAttendance({});
  
  // Reset session storage for a new check-in
  const today = new Date();
  const formattedDate = today.toISOString().split('T')[0];
  const lastCheckInDate = localStorage.getItem('lastCheckInDate');
  
  // If it's a new day, clear all session storage flags
  if (lastCheckInDate !== formattedDate) {
    sessionStorage.removeItem('hasShownGoalPopup');
    sessionStorage.removeItem('hasShownLateCheckInPopup');
    sessionStorage.removeItem('hasShownEarlyCheckOutPopup');
    sessionStorage.removeItem('hasShownTodayStatusPopup');
    sessionStorage.removeItem('hasShownOverLimitBreakPopup');
    localStorage.setItem('lastCheckInDate', formattedDate);
  }
  
  // Show goal popup - the actual check-in will happen after goal submission
  setShowGoalPopup(true);
};

  const handleSubmitGoal = (goalData) => {
  console.log("Goal submitted:", goalData);
  
  // Set checking in state
  setCheckingIn(true);
  
  // Close the goal popup
  setShowGoalPopup(false);
  
  // Mark as shown to prevent re-showing
  sessionStorage.setItem('hasShownGoalPopup', 'true');
  
  // Check if profile exists
  if (!profile || !profile._id) {
    console.error("Profile data is missing");
    setCheckingIn(false);
    return;
  }
  
  // Create the goal first
  if (goalData.todaysGoal) {
    dispatch(ActivityActions.createTodayGoal({
      id: profile._id,
      todaysGoal: goalData.todaysGoal
    }));
  }
  
  // Then perform check-in with current timestamp
  const checkInTime = new Date();
  dispatch(
    AttendanceActions.createAttendance({
      user: profile._id,
      checkIn: checkInTime,
    })
  );
  
  // Force immediate refresh of attendance data with properly formatted date
  const today = new Date();
  const formattedDate = today.toISOString().split('T')[0];
  const timestamp = Date.now();
  
  // Store the check-in date in localStorage
  localStorage.setItem('lastCheckInDate', formattedDate);
  
  // Add a small delay before fetching updated attendance data
  setTimeout(() => {
    dispatch(
      AttendanceActions.getAttendances({
        user: profile._id,
        date: formattedDate,
        _t: timestamp, // Add timestamp to prevent caching
      })
    );
    
    // Update controller state
    setController(!controller);
    
    // Reset checking state
    setCheckingIn(false);
  }, 1000);
};
  // Handle user check-out action
  const handleCheckOut = () => {
    if (checkingOut) { return; }
    
    // Check if profile and attendance data exist
    if (!profile || !profile._id || !attendance || !attendance._id || !todayActivity || !todayActivity.length) {
      console.error("Missing required data for check-out");
      return;
    }
    
    setCheckingOut(true);
    setController(!controller);
    
    dispatch(
      AttendanceActions.updateAttendance({
        id: attendance._id,
        checkOut: new Date(),
        user: profile._id
      })
    );

    dispatch(
      ActivityActions.checkOutStatusUpdate({
        _id: todayActivity[0]._id,
        user: profile._id,
      })
    );

    setTimeout(() => {
      setCheckingOut(false);
    }, 2000);
  };

  // Listen for checkout success to show work hours status
  const checkoutSuccess = useSelector(state =>
    state.general.success.find(s => s.action === ActivityActions.checkOutStatusUpdate.type)
  );

  // Update work hours status when checkout is successful
  useEffect(() => {
    if (checkoutSuccess && checkoutSuccess.data) {
      setWorkHoursStatus(checkoutSuccess.data);
      setShowWorkHoursStatus(true);
      setTimeout(() => {
        dispatch(GeneralActions.removeSuccess(ActivityActions.checkOutStatusUpdate.type));
      }, 500);
    }
  }, [checkoutSuccess, dispatch]);

  // Handle break type selection
  const handleBreakOut = (event) => {
    setLunchType(event.target.value);
  };

  // Control the visibility of the break reason popup
  function breakPopUpController(val) {
    setShowBreakPop(val);
  }

  // Check if user has permission to view attendance
  if (!Can(actions.readSelf, features.attendance)) {
    return <div></div>;
  }

  // UI
  return (
    <Grid container spacing={3} sx={{ padding: "20px", display: "flex", alignItems: "center" }}>
      {/* Productivity Chart */}
      <Grid item xs={12} md={8}>
        <Card sx={{ height: "100%", padding: "20px" }}>
          <ProductivityChart todayActivities={todayActivity} />
          {/* <TaskProgressBar todayActivities={todayActivity} /> */}
        </Card>
      </Grid>

      {/* Activity Card */}
      <Grid item xs={12} md={4}>
        <Card
          sx={{
            height: "100%",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
          }}
        >
          <Timer color="primary" sx={{ fontSize: 100 }} />

          <Box>
            <Typography variant="h5" align="center" gutterBottom sx={{ padding: "10px", margin: "10px" }}>
              Your Activity Today
            </Typography>

            <InfoLog>
              {["Check In", "Check Out"].map((label, index) => (
                <Box key={label} sx={{ textAlign: "center" }}>
                  <Typography>{label}</Typography>
                  <Typography variant="subtitle2">
                    {attendance[index ? "checkOut" : "checkIn"] ? moment(attendance[index ? "checkOut" : "checkIn"]).format("HH:mm") : "-"}
                  </Typography>
                </Box>
              ))}
            </InfoLog>
          </Box>

          {!attendance.checkOut && (
            <Box sx={{ mt: 3 }}>
              {/* Check In button */}
              {!attendance.checkIn ? (
                <Button
                  fullWidth
                  variant="contained"
                  onClick={handleCheckIn}
                  disabled={checkingIn}
                  sx={{
                    backgroundColor: checkingIn ? "#a5d6a7" : "green",
                    color: "white",
                    fontWeight: "bold",
                    borderRadius: "8px",
                    width: "150px",
                    height: "50px",
                    boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.2)",
                    display: "flex",
                    alignItems: "center",
                    "&:hover": { backgroundColor: checkingIn ? "#a5d6a7" : "darkgreen" },
                  }}
                >
                  {checkingIn ? "Processing..." : "Check In"}
                </Button>
              ) : (
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    width: "100%",
                    marginTop: "10px",
                    marginBottom: "10px",
                    gap: "10px",
                  }}
                >
                  {/* Break controls */}
                  {todayActivity.length > 0 && todayActivity[0]?.breakStatus ? (
                    <FormControl>
                      <Select
                        labelId="break-label"
                        value={lunchType}
                        label="Break Type"
                        onChange={handleBreakOut}
                        sx={{
                          backgroundColor: "#2e7d32",
                          color: "white",
                          fontWeight: "bold",
                          borderRadius: "8px",
                          width: "150px",
                          height: "50px",
                          boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.2)",
                          display: "flex",
                          alignItems: "center",
                        }}
                        renderValue={() => "----"}
                      >
                        <MenuItem value="breakOut">Break Out</MenuItem>
                      </Select>
                    </FormControl>
                  ) : (
                    <FormControl>
                      <Select
                        labelId="break-label"
                        value={lunchType || ""}
                        label="Break Type"
                        onChange={handleBreakOut}
                        sx={{
                          backgroundColor: "#2e7d32",
                          color: "white",
                          fontWeight: "bold",
                          borderRadius: "8px",
                          width: "150px",
                          height: "50px",
                          boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.2)",
                          display: "flex",
                          alignItems: "center",
                        }}
                        renderValue={() => "Break In"}
                      >
                        <MenuItem value="" disabled>Break In</MenuItem>
                        <MenuItem value="lunchBreak">Lunch Break</MenuItem>
                        <MenuItem value="teaBreak">Tea Break</MenuItem>
                        <MenuItem value="other">Other</MenuItem>
                      </Select>
                    </FormControl>
                  )}

                  {/* Check Out button */}
                  <Button
                    variant="contained"
                    color="error"
                    onClick={handleCheckOut}
                    disabled={checkingOut}
                    sx={{
                      backgroundColor: checkingOut ? "#ef9a9a" : "darked",
                      color: "white",
                      fontWeight: "bold",
                      borderRadius: "8px",
                      width: "150px",
                      height: "50px",
                      boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.2)",
                      display: "flex",
                      alignItems: "center",
                    }}
                  >
                    {checkingOut ? "Processing..." : "Check Out"}
                  </Button>
                </div>
              )}
            </Box>
          )}

          {/* Popups */}
          {showGoalPopup && todayActivity.length === 0 && !sessionStorage.getItem('hasShownGoalPopup') && (
  <TodayGoal
    title="Today's Goal"
    task="goal"
    onSubmit={handleSubmitGoal}  // Fixed typo
    onClose={() => {
      setShowGoalPopup(false);
      sessionStorage.setItem('hasShownGoalPopup', 'true');
    }}
    required={true}  // Make goal mandatory
  />
)}
           

          {showBreakPop && (
            <OtherBreak
              openVal={true}
              settingFun={breakPopUpController}
              _id={todayActivity[0]._id}
              profile={profile}
            />
          )}

          {lateCheckIn &&  (
            <EarlyLate
              openVal={true}
              task="late"
              dialogTitle="Late Check In"
              id={todayActivity[0]._id}
              onClose={() => {
                // sessionStorage.setItem('hasShownLateCheckInPopup', 'true');
                setLateCheckIn(false);
              }}
            />
          )}

          {todayStatus &&  (
            <EarlyLate
              openVal={true}
              task="status"
              dialogTitle="Work Status"
              id={todayActivity[0]._id}
              onClose={() => {
                // sessionStorage.setItem('hasShownTodayStatusPopup', 'true');
                setTodayStatus(false);
              }}
            />
          )}

          {earlyCheckOut &&  (
            <EarlyLate
              openVal={true}
              task="early"
              dialogTitle="Early Check Out"
              id={todayActivity[0]._id}
              onClose={() => {
                // sessionStorage.setItem('hasShownEarlyCheckOutPopup', 'true');
                setEarlyCheckOut(false);
              }}
            />
          )}

          {overLimitBreak &&  (
            <OverLimitBreak
              openVal={true}
              id={todayActivity[0]._id}
              onClose={() => {
                // sessionStorage.setItem('hasShownOverLimitBreakPopup', 'true');
                setOverLimitBreak(false);
              }}
            />
          )}

          {/* Work Hours Status Dialog */}
          <WorkHoursStatus
            open={showWorkHoursStatus}
            statusData={workHoursStatus}
            onClose={() => setShowWorkHoursStatus(false)}
          />
        </Card>
      </Grid>

      {/* Bottom Section */}
      <Grid item xs={12}>
        <Grid container spacing={3} sx={{ display: "flex", alignItems: "center" }}>
          {/* Attendance Bar Chart */}
          <Grid item xs={12} md={6}>
            <AttendanceBarChart activities={activities} />
          </Grid>

          {/* Leave Information */}
          {Can(actions.readSelf, features.leave) && (
            <Grid item xs={12} md={6} container spacing={2}>
              <Grid item xs={12}>
                <Widget
                  title="Leave Taken"
                  content={countLeave ?? 0}
                  icon={<AssignmentIndOutlined sx={{ color: theme.palette.primary.main, fontSize: 62 }} />}
                />
              </Grid>
              {Can(actions.read, features.attendance) && (
                <Grid item xs={12}>
                  <Widget
                    title="Quote Leave"
                    content={setting?.leaveLimit ?? 0}
                    icon={<AssignmentIndOutlined sx={{ color: theme.palette.primary.main, fontSize: 62 }} />}
                  />
                </Grid>
              )}
            </Grid>
          )}
        </Grid>
      </Grid>
    </Grid>
  );

}