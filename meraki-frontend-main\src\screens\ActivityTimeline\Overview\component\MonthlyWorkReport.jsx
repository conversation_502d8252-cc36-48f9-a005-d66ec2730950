import React, { useEffect } from "react";
import PropTypes from "prop-types";
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Paper,
  LinearProgress,
  Avatar
} from "@mui/material";
import { useSelector, useDispatch } from "react-redux";
import { format, parseISO } from "date-fns";
import { ActivityActions } from "../../../../slices/actions";

const MonthlyWorkReport = ({ dateRange }) => {
  const dispatch = useDispatch();
  const { multiUserActivityArr } = useSelector((state) => state.activity || { multiUserActivityArr: [] });

  // Use multiUserActivityArr for the ActivityTimeline components
  const activityArr = multiUserActivityArr;
  
  // Format the selected month for display
  const displayMonth = dateRange?.startDate ? format(parseISO(dateRange.startDate), "MMMM yyyy") : format(new Date(), "MMMM yyyy");

  useEffect(() => {
    if (dateRange?.startDate && dateRange?.endDate) {
      dispatch(ActivityActions.getUserActivity({
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
        view: 'month'
      }));
    }
  }, [dateRange, dispatch]);

  // If data is not available, show placeholder
  if (!activityArr || activityArr.length === 0) {
    return (
      <Box p={3}>
        <Typography variant="h6" gutterBottom>
          Monthly Work Report – {displayMonth}
        </Typography>
        <Typography>No employee data available</Typography>
      </Box>
    );
  }

  return (
    <Box p={3}>
      <Typography variant="h6" gutterBottom>
        Monthly Work Report – {displayMonth}
      </Typography>
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: "#f5f5f5" }}>
              <TableCell>Name</TableCell>
              <TableCell>Total Work Hours</TableCell>
              <TableCell>Worked Hours</TableCell>
              <TableCell>Focus Hours</TableCell>
              <TableCell>Productive Hours</TableCell>
              <TableCell>Idle + Private</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {activityArr.map((emp, idx) => {
              // Calculate progress based on total work vs worked hours
              const totalWork = emp.totalWork || "0h 0m";
              const worked = emp.worked || "0h 0m";
              
              // Determine color based on worked vs total work ratio
              let color = "inherit";
              if (emp.worked === "--") {
                color = "inherit";
              } else if (parseFloat(emp.worked) >= parseFloat(emp.totalWork) * 0.9) {
                color = "success";
              } else if (parseFloat(emp.worked) >= parseFloat(emp.totalWork) * 0.6) {
                color = "warning";
              } else {
                color = "error";
              }
              
              return (
                <TableRow key={idx}>
                  <TableCell>
                    <Box display="flex" alignItems="center" gap={1}>
                      <Avatar>{(emp.name || "")[0]}</Avatar>
                      {emp.name || ""}
                    </Box>
                  </TableCell>
                  <TableCell>
                    {totalWork}
                    <LinearProgress
                      variant="determinate"
                      value={100}
                      color={color}
                      sx={{ height: 6, borderRadius: 5, mt: 0.5 }}
                    />
                  </TableCell>
                  <TableCell>{worked}</TableCell>
                  <TableCell>{emp.focus || "--"}</TableCell>
                  <TableCell>{emp.productive || "--"}</TableCell>
                  <TableCell>{emp.idle || "--"}</TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

MonthlyWorkReport.propTypes = {
  dateRange: PropTypes.shape({
    startDate: PropTypes.string,
    endDate: PropTypes.string
  })
};

export default MonthlyWorkReport;