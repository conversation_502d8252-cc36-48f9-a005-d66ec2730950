{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\ActivityTimeline\\\\Overview\\\\component\\\\WeekWorkReport.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport PropTypes from \"prop-types\";\nimport { Avatar, Box, Card, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography } from \"@mui/material\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { format, parseISO, addDays, startOfWeek, endOfWeek } from \"date-fns\";\nimport { ActivityActions } from \"../../../../slices/actions\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst getInitials = name => name.split(\" \").map(n => n[0]).join(\"\");\nconst getColor = value => {\n  if (value === \"Holiday\") {\n    return \"red\";\n  }\n  if (value === \"--\") {\n    return \"gray\";\n  }\n  const hours = parseInt(value.slice(0, 2), 10);\n  return hours >= 8 ? \"green\" : \"red\";\n};\nconst WeekWorkReport = ({\n  dateRange\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    multiUserActivityArr\n  } = useSelector(state => state.activity || {\n    multiUserActivityArr: []\n  });\n\n  // Use multiUserActivityArr for the ActivityTimeline components\n  const activityArr = multiUserActivityArr;\n  const [weekDays, setWeekDays] = useState([]);\n\n  // Format the selected week range for display\n  const startDate = dateRange !== null && dateRange !== void 0 && dateRange.startDate ? parseISO(dateRange.startDate) : startOfWeek(new Date());\n  const endDate = dateRange !== null && dateRange !== void 0 && dateRange.endDate ? parseISO(dateRange.endDate) : endOfWeek(new Date());\n  const displayWeekRange = `${format(startDate, \"EEE, MMM d, yyyy\")} – ${format(endDate, \"EEE, MMM d, yyyy\")}`;\n\n  // Generate week days array based on date range\n  useEffect(() => {\n    if (dateRange !== null && dateRange !== void 0 && dateRange.startDate && dateRange !== null && dateRange !== void 0 && dateRange.endDate) {\n      const start = parseISO(dateRange.startDate);\n      const days = [];\n\n      // Generate array of days in the week\n      for (let i = 0; i < 7; i++) {\n        const day = addDays(start, i);\n        days.push(format(day, \"MMM dd\"));\n      }\n      setWeekDays(days);\n\n      // Fetch activity data for the week\n      dispatch(ActivityActions.getUserActivity({\n        startDate: dateRange.startDate,\n        endDate: dateRange.endDate,\n        view: 'week'\n      }));\n    }\n  }, [dateRange, dispatch]);\n\n  // If data is not available, show placeholder\n  if (!activityArr || activityArr.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      p: 3,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: displayWeekRange\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        children: \"No employee data available\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    p: 3,\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      mb: 2,\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: displayWeekRange\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Card,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this), weekDays.map((day, i) => /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              children: day\n            }, i, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Total\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: activityArr.map((emp, i) => /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 1,\n                children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                  children: getInitials(emp.name || \"\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 101,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: emp.name || \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 102,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 17\n            }, this), (emp.weekData || Array(7).fill(\"--\")).map((time, idx) => /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              sx: {\n                color: getColor(time),\n                fontWeight: \"bold\"\n              },\n              children: time\n            }, idx, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 19\n            }, this)), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: \"bold\"\n              },\n              children: emp.total || \"0h 00m\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 17\n            }, this)]\n          }, i, true, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"caption\",\n      color: \"gray\",\n      mt: 2,\n      display: \"block\",\n      children: [\"\\u2139\\uFE0F Calculation based on \", /*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Time at Work\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 33\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 79,\n    columnNumber: 5\n  }, this);\n};\n_s(WeekWorkReport, \"qf94FIE0fG3aBu7iLej7sxDa2Bc=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = WeekWorkReport;\nWeekWorkReport.propTypes = {\n  dateRange: PropTypes.shape({\n    startDate: PropTypes.string,\n    endDate: PropTypes.string\n  })\n};\nexport default WeekWorkReport;\nvar _c;\n$RefreshReg$(_c, \"WeekWorkReport\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "PropTypes", "Avatar", "Box", "Card", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Typography", "useSelector", "useDispatch", "format", "parseISO", "addDays", "startOfWeek", "endOfWeek", "ActivityActions", "jsxDEV", "_jsxDEV", "getInitials", "name", "split", "map", "n", "join", "getColor", "value", "hours", "parseInt", "slice", "WeekWorkReport", "date<PERSON><PERSON><PERSON>", "_s", "dispatch", "multiUserActivityArr", "state", "activity", "activityArr", "weekDays", "setWeekDays", "startDate", "Date", "endDate", "displayWeekRange", "start", "days", "i", "day", "push", "getUserActivity", "view", "length", "p", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "justifyContent", "alignItems", "mb", "component", "align", "emp", "gap", "weekData", "Array", "fill", "time", "idx", "sx", "color", "fontWeight", "total", "mt", "_c", "propTypes", "shape", "string", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/ActivityTimeline/Overview/component/WeekWorkReport.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport PropTypes from \"prop-types\";\nimport {\n  Avatar,\n  Box,\n  Card,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Typography\n} from \"@mui/material\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { format, parseISO, addDays, startOfWeek, endOfWeek } from \"date-fns\";\nimport { ActivityActions } from \"../../../../slices/actions\";\n\nconst getInitials = (name) =>\n  name.split(\" \").map((n) => n[0]).join(\"\");\n\nconst getColor = (value) => {\n  if (value === \"Holiday\") { return \"red\" }\n  if (value === \"--\") { return \"gray\" }\n  const hours = parseInt(value.slice(0, 2), 10);\n  return hours >= 8 ? \"green\" : \"red\";\n};\n\nconst WeekWorkReport = ({ dateRange }) => {\n  const dispatch = useDispatch();\n  const { multiUserActivityArr } = useSelector((state) => state.activity || { multiUserActivityArr: [] });\n\n  // Use multiUserActivityArr for the ActivityTimeline components\n  const activityArr = multiUserActivityArr;\n  const [weekDays, setWeekDays] = useState([]);\n  \n  // Format the selected week range for display\n  const startDate = dateRange?.startDate ? parseISO(dateRange.startDate) : startOfWeek(new Date());\n  const endDate = dateRange?.endDate ? parseISO(dateRange.endDate) : endOfWeek(new Date());\n  \n  const displayWeekRange = `${format(startDate, \"EEE, MMM d, yyyy\")} – ${format(endDate, \"EEE, MMM d, yyyy\")}`;\n\n  // Generate week days array based on date range\n  useEffect(() => {\n    if (dateRange?.startDate && dateRange?.endDate) {\n      const start = parseISO(dateRange.startDate);\n      const days = [];\n      \n      // Generate array of days in the week\n      for (let i = 0; i < 7; i++) {\n        const day = addDays(start, i);\n        days.push(format(day, \"MMM dd\"));\n      }\n      \n      setWeekDays(days);\n      \n      // Fetch activity data for the week\n      dispatch(ActivityActions.getUserActivity({\n        startDate: dateRange.startDate,\n        endDate: dateRange.endDate,\n        view: 'week'\n      }));\n    }\n  }, [dateRange, dispatch]);\n\n  // If data is not available, show placeholder\n  if (!activityArr || activityArr.length === 0) {\n    return (\n      <Box p={3}>\n        <Typography variant=\"h6\" gutterBottom>\n          {displayWeekRange}\n        </Typography>\n        <Typography>No employee data available</Typography>\n      </Box>\n    );\n  }\n\n  return (\n    <Box p={3}>\n      <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\n        <Typography variant=\"h6\">\n          {displayWeekRange}\n        </Typography>\n      </Box>\n      <TableContainer component={Card}>\n        <Table>\n          <TableHead>\n            <TableRow>\n              <TableCell><strong>Name</strong></TableCell>\n              {weekDays.map((day, i) => (\n                <TableCell key={i} align=\"center\">{day}</TableCell>\n              ))}\n              <TableCell><strong>Total</strong></TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {activityArr.map((emp, i) => (\n              <TableRow key={i}>\n                <TableCell>\n                  <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                    <Avatar>{getInitials(emp.name || \"\")}</Avatar>\n                    <Typography>{emp.name || \"\"}</Typography>\n                  </Box>\n                </TableCell>\n                {(emp.weekData || Array(7).fill(\"--\")).map((time, idx) => (\n                  <TableCell\n                    key={idx}\n                    align=\"center\"\n                    sx={{ color: getColor(time), fontWeight: \"bold\" }}\n                  >\n                    {time}\n                  </TableCell>\n                ))}\n                <TableCell sx={{ fontWeight: \"bold\" }}>{emp.total || \"0h 00m\"}</TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n      </TableContainer>\n      <Typography variant=\"caption\" color=\"gray\" mt={2} display=\"block\">\n        ℹ️ Calculation based on <strong>Time at Work</strong>\n      </Typography>\n    </Box>\n  );\n};\n\nWeekWorkReport.propTypes = {\n  dateRange: PropTypes.shape({\n    startDate: PropTypes.string,\n    endDate: PropTypes.string\n  })\n};\n\nexport default WeekWorkReport;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,SAAS,MAAM,YAAY;AAClC,SACEC,MAAM,EACNC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,UAAU,QACL,eAAe;AACtB,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,WAAW,EAAEC,SAAS,QAAQ,UAAU;AAC5E,SAASC,eAAe,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,WAAW,GAAIC,IAAI,IACvBA,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;AAE3C,MAAMC,QAAQ,GAAIC,KAAK,IAAK;EAC1B,IAAIA,KAAK,KAAK,SAAS,EAAE;IAAE,OAAO,KAAK;EAAC;EACxC,IAAIA,KAAK,KAAK,IAAI,EAAE;IAAE,OAAO,MAAM;EAAC;EACpC,MAAMC,KAAK,GAAGC,QAAQ,CAACF,KAAK,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EAC7C,OAAOF,KAAK,IAAI,CAAC,GAAG,OAAO,GAAG,KAAK;AACrC,CAAC;AAED,MAAMG,cAAc,GAAGA,CAAC;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EACxC,MAAMC,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEwB;EAAqB,CAAC,GAAGzB,WAAW,CAAE0B,KAAK,IAAKA,KAAK,CAACC,QAAQ,IAAI;IAAEF,oBAAoB,EAAE;EAAG,CAAC,CAAC;;EAEvG;EACA,MAAMG,WAAW,GAAGH,oBAAoB;EACxC,MAAM,CAACI,QAAQ,EAAEC,WAAW,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;;EAE5C;EACA,MAAM2C,SAAS,GAAGT,SAAS,aAATA,SAAS,eAATA,SAAS,CAAES,SAAS,GAAG5B,QAAQ,CAACmB,SAAS,CAACS,SAAS,CAAC,GAAG1B,WAAW,CAAC,IAAI2B,IAAI,CAAC,CAAC,CAAC;EAChG,MAAMC,OAAO,GAAGX,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEW,OAAO,GAAG9B,QAAQ,CAACmB,SAAS,CAACW,OAAO,CAAC,GAAG3B,SAAS,CAAC,IAAI0B,IAAI,CAAC,CAAC,CAAC;EAExF,MAAME,gBAAgB,GAAG,GAAGhC,MAAM,CAAC6B,SAAS,EAAE,kBAAkB,CAAC,MAAM7B,MAAM,CAAC+B,OAAO,EAAE,kBAAkB,CAAC,EAAE;;EAE5G;EACA9C,SAAS,CAAC,MAAM;IACd,IAAImC,SAAS,aAATA,SAAS,eAATA,SAAS,CAAES,SAAS,IAAIT,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEW,OAAO,EAAE;MAC9C,MAAME,KAAK,GAAGhC,QAAQ,CAACmB,SAAS,CAACS,SAAS,CAAC;MAC3C,MAAMK,IAAI,GAAG,EAAE;;MAEf;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC1B,MAAMC,GAAG,GAAGlC,OAAO,CAAC+B,KAAK,EAAEE,CAAC,CAAC;QAC7BD,IAAI,CAACG,IAAI,CAACrC,MAAM,CAACoC,GAAG,EAAE,QAAQ,CAAC,CAAC;MAClC;MAEAR,WAAW,CAACM,IAAI,CAAC;;MAEjB;MACAZ,QAAQ,CAACjB,eAAe,CAACiC,eAAe,CAAC;QACvCT,SAAS,EAAET,SAAS,CAACS,SAAS;QAC9BE,OAAO,EAAEX,SAAS,CAACW,OAAO;QAC1BQ,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACnB,SAAS,EAAEE,QAAQ,CAAC,CAAC;;EAEzB;EACA,IAAI,CAACI,WAAW,IAAIA,WAAW,CAACc,MAAM,KAAK,CAAC,EAAE;IAC5C,oBACEjC,OAAA,CAAClB,GAAG;MAACoD,CAAC,EAAE,CAAE;MAAAC,QAAA,gBACRnC,OAAA,CAACV,UAAU;QAAC8C,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAClCV;MAAgB;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eACbzC,OAAA,CAACV,UAAU;QAAA6C,QAAA,EAAC;MAA0B;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC;EAEV;EAEA,oBACEzC,OAAA,CAAClB,GAAG;IAACoD,CAAC,EAAE,CAAE;IAAAC,QAAA,gBACRnC,OAAA,CAAClB,GAAG;MAAC4D,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,eAAe;MAACC,UAAU,EAAC,QAAQ;MAACC,EAAE,EAAE,CAAE;MAAAV,QAAA,eAC3EnC,OAAA,CAACV,UAAU;QAAC8C,OAAO,EAAC,IAAI;QAAAD,QAAA,EACrBV;MAAgB;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eACNzC,OAAA,CAACb,cAAc;MAAC2D,SAAS,EAAE/D,IAAK;MAAAoD,QAAA,eAC9BnC,OAAA,CAAChB,KAAK;QAAAmD,QAAA,gBACJnC,OAAA,CAACZ,SAAS;UAAA+C,QAAA,eACRnC,OAAA,CAACX,QAAQ;YAAA8C,QAAA,gBACPnC,OAAA,CAACd,SAAS;cAAAiD,QAAA,eAACnC,OAAA;gBAAAmC,QAAA,EAAQ;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,EAC3CrB,QAAQ,CAAChB,GAAG,CAAC,CAACyB,GAAG,EAAED,CAAC,kBACnB5B,OAAA,CAACd,SAAS;cAAS6D,KAAK,EAAC,QAAQ;cAAAZ,QAAA,EAAEN;YAAG,GAAtBD,CAAC;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAiC,CACnD,CAAC,eACFzC,OAAA,CAACd,SAAS;cAAAiD,QAAA,eAACnC,OAAA;gBAAAmC,QAAA,EAAQ;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZzC,OAAA,CAACf,SAAS;UAAAkD,QAAA,EACPhB,WAAW,CAACf,GAAG,CAAC,CAAC4C,GAAG,EAAEpB,CAAC,kBACtB5B,OAAA,CAACX,QAAQ;YAAA8C,QAAA,gBACPnC,OAAA,CAACd,SAAS;cAAAiD,QAAA,eACRnC,OAAA,CAAClB,GAAG;gBAAC4D,OAAO,EAAC,MAAM;gBAACE,UAAU,EAAC,QAAQ;gBAACK,GAAG,EAAE,CAAE;gBAAAd,QAAA,gBAC7CnC,OAAA,CAACnB,MAAM;kBAAAsD,QAAA,EAAElC,WAAW,CAAC+C,GAAG,CAAC9C,IAAI,IAAI,EAAE;gBAAC;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,eAC9CzC,OAAA,CAACV,UAAU;kBAAA6C,QAAA,EAAEa,GAAG,CAAC9C,IAAI,IAAI;gBAAE;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,EACX,CAACO,GAAG,CAACE,QAAQ,IAAIC,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,EAAEhD,GAAG,CAAC,CAACiD,IAAI,EAAEC,GAAG,kBACnDtD,OAAA,CAACd,SAAS;cAER6D,KAAK,EAAC,QAAQ;cACdQ,EAAE,EAAE;gBAAEC,KAAK,EAAEjD,QAAQ,CAAC8C,IAAI,CAAC;gBAAEI,UAAU,EAAE;cAAO,CAAE;cAAAtB,QAAA,EAEjDkB;YAAI,GAJAC,GAAG;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKC,CACZ,CAAC,eACFzC,OAAA,CAACd,SAAS;cAACqE,EAAE,EAAE;gBAAEE,UAAU,EAAE;cAAO,CAAE;cAAAtB,QAAA,EAAEa,GAAG,CAACU,KAAK,IAAI;YAAQ;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA,GAhB7Db,CAAC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiBN,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eACjBzC,OAAA,CAACV,UAAU;MAAC8C,OAAO,EAAC,SAAS;MAACoB,KAAK,EAAC,MAAM;MAACG,EAAE,EAAE,CAAE;MAACjB,OAAO,EAAC,OAAO;MAAAP,QAAA,GAAC,oCACxC,eAAAnC,OAAA;QAAAmC,QAAA,EAAQ;MAAY;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAAC3B,EAAA,CAhGIF,cAAc;EAAA,QACDpB,WAAW,EACKD,WAAW;AAAA;AAAAqE,EAAA,GAFxChD,cAAc;AAkGpBA,cAAc,CAACiD,SAAS,GAAG;EACzBhD,SAAS,EAAEjC,SAAS,CAACkF,KAAK,CAAC;IACzBxC,SAAS,EAAE1C,SAAS,CAACmF,MAAM;IAC3BvC,OAAO,EAAE5C,SAAS,CAACmF;EACrB,CAAC;AACH,CAAC;AAED,eAAenD,cAAc;AAAC,IAAAgD,EAAA;AAAAI,YAAA,CAAAJ,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}