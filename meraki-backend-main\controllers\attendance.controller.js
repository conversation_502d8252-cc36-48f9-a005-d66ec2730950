'use strict';

const AttendanceService = require("../services/attendance.service");
const moment = require("moment");
const mongoose = require("mongoose");

exports.fetchAllAttendances = async (req, res) => {
    const { user, sort, page, limit, department, designation, date } = req.query;

    console.log(" FETCh ALL ATTENDANCES ",user, sort, page, limit, department, designation, date)
    const queries = {
        page: page ? parseInt(page) : 1,
        limit: limit ? parseInt(limit) : 20,
        sort: { name: -1 },
        query: []
    };

    if (sort) {
        const field = sort.split(",");
        queries.sort = {
            [field[0]]: parseInt(field[1])
        };
    }

    if (date) {
        queries.query.push({
            checkIn: {
                $gte: moment(new Date(date)).startOf('day').toDate(),
                $lt: moment(new Date(date)).endOf('day').toDate()
            }
        });
    }

    if (user) {
        queries.query.push({ 'user._id': mongoose.Types.ObjectId(user) });
    }

    if (department) {
        queries.query.push({ 'user.department': mongoose.Types.ObjectId(department) })
    }

    if (designation) {
        queries.query.push({ 'user.designation': mongoose.Types.ObjectId(designation) })
    }
    console.warn("Quries ",queries)
    let results = await AttendanceService.getAttendancesByQuery(queries);
    console.warn("get Atteandeces results ", results)
    return res.status(200).send(results);
}

exports.fetchAttendanceUserToday = async (req, res) => {
    const { user } = req;

    // const result = await AttendanceService.getAttendanceByUser(user);
    // const today = result.find(item => moment(item.checkIn).isSame(new Date(), 'day'))

    return res.send([]);
}

exports.createAttendance = async (req, res) => {
    try {
        const { body } = req;

        // Validate user ID
        if (!body.user || typeof body.user !== 'string' || body.user.trim() === '') {
            return res.status(400).send({
                message: "Invalid user ID. User ID is required."
            });
        }

        // Ensure checkIn is a valid date
        if (!body.checkIn) {
            body.checkIn = new Date();
        }

        // Create attendance record
        const result = await AttendanceService.createAttendance(body);

        if (!result) {
            return res.status(500).send({
                message: "Failed to create attendance record."
            });
        }

        // Initialize activity record with the same timestamp
        // This ensures the checkInTime in Activity matches the checkIn in Attendance
    
        // const Activity = require('../models').db.activity;
        // const existingActivity = await Activity.findOne({
        //     user: body.user,
        //     checkInTime: {
        //         $gte: new Date(new Date().setHours(0, 0, 0, 0)),
        //         $lt: new Date(new Date().setHours(23, 59, 59, 999))
        //     }
        // });

        // Only create activity if one doesn't already exist for today
        // if (!existingActivity) { 
        //     await Activity.create({
        //         user: body.user,
        //         checkInTime: body.checkIn, // Use the same timestamp
        //         lateCheckInStatus: new Date(body.checkIn).getHours() >= 10 // Simple late check-in logic
        //     });
        // }

        // return res.status(200).send({
        //     message: "Successfully created attendance record.",
        //     _id: result._id
        // });
    } catch (error) {
        return res.status(500).send({
            message: "Error creating attendance record",
            error: error.message
        });
    }
        
}

exports.fetchAttendanceById = async (req, res) => {
    const { params } = req;

    const result = await AttendanceService.getAttendanceById(params.id);

    if (!result) {
        return res.status(500).send({
            message: "Failed to proceed data."
        })
    }

    return res.status(200).send(result);
}

exports.fetchLoggedInAttendance = async (req, res) => {
    const { Attendance } = req;
    const result = await AttendanceService.getAttendanceById(Attendance);
    if (!result) {
        return res.status(500).send({
            message: "Failed to proceed data."
        })
    }
    return res.status(200).send(result);
}

exports.updateAttendance = async (req, res) => {
    const { params, body } = req;
    const result = await AttendanceService.updateAttendance(params.id, body);
    if (!result || result.n === 0) {
        return res.status(500).send({
            message: "Failed to proceed data."
        })
    }
    return res.status(200).send({
        message: "Successfully proceed data."
    });
}

exports.deleteAttendance = async (req, res) => {
    const { params } = req;
    const result = await AttendanceService.deleteAttendance(params.id);
    if (!result) {
        return res.status(500).send({
            message: "Failed to proceed data."
        })
    }
    return res.status(200).send({
        message: "Successfully proceed data."
    });
}

exports.createLunchBreak = async (req,res) => {
    const { params,body } = req;

    const result = await AttendanceService.getAttendanceById(params.id);
    if (!result || result.n === 0) {
        return res.status(500).send({
            message: "Failed to proceed data."
        })
    }
    result.lunchIn.push(body.lunchIn)
    result.save()

    return res.status(200).send({
        message: "Successfully proceed data."
    });

}

/**
 * Update lunch break end time
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Success message or overlimit break notification
 */
exports.updateLunchBreak = async (req, res) => {
    try {
        const { params, body } = req;

        // Get attendance record
        const attendance = await AttendanceService.getAttendanceById(params.id);

        if (!attendance) {
            return res.status(404).send({
                message: "Attendance record not found"
            });
        }

        // Add lunch out time
        attendance.lunchOut.push(body.lunchOut);
        await attendance.save();

        // Calculate break duration in milliseconds
        const breakDuration = Date.now() - attendance.lunchIn[attendance.lunchIn.length - 1];
        const THIRTY_MINUTES = 30 * 60 * 1000; // 30 minutes in milliseconds

        // Check if break is within limit (30 minutes)
        if (breakDuration < THIRTY_MINUTES) {
            return res.status(200).json({
                message: "Successfully recorded lunch break end"
            });
        } else {
            return res.status(200).json({
                message: "Overlimit break",
                duration: Math.floor(breakDuration / 60000) // Duration in minutes
            });
        }
    } catch (error) {
        return res.status(500).send({
            message: "Failed to update lunch break",
            error: error.message
        });
    }
}

/**
 * Fetch attendance records for a specific month
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Attendance records with pagination
 */
exports.fetchAttendanceByMonth = async (req, res) => {
    try {
        const queries = {
            page: 1,
            limit: 20,
            sort: { name: -1 },
            query: []
        };

        const result = await AttendanceService.getAttendanceByMonth(req.params, queries);
        return res.status(200).send(result);
    } catch (error) {
        return res.status(500).send({
            message: "Failed to fetch attendance records",
            error: error.message
        });
    }
}

exports.deleteCheckOut = async (req,res) => {
 
    const {id} = req.body
    const result = await AttendanceService.deleteCheckOut(id)
 
    return res.status(200).send(result)
}