{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\ActivityTimeline\\\\Overview\\\\component\\\\DayWorkReport.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from \"react\";\nimport PropTypes from \"prop-types\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { Avatar, Box, Card, Typography, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, LinearProgress } from \"@mui/material\";\nimport { format, parseISO } from \"date-fns\";\nimport { ActivityActions } from \"../../../../slices/actions\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst getInitials = name => name.split(\" \").map(n => n[0]).join(\"\");\nconst formatTime = timeStr => {\n  if (!timeStr || timeStr === \"--\") {\n    return \"--\";\n  }\n  const minutes = parseInt(timeStr, 10); // 👈 radix added\n  if (isNaN(minutes)) {\n    return timeStr;\n  }\n  const hours = Math.floor(minutes / 60);\n  const mins = minutes % 60;\n  return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;\n};\nconst DayWorkReportFull = ({\n  dateRange\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    multiUserActivityArr\n  } = useSelector(state => state.Activity || {\n    multiUserActivityArr: []\n  });\n\n  // Use multiUserActivityArr for the ActivityTimeline components\n  const activityArr = multiUserActivityArr;\n\n  // Format the selected date for display\n  const displayDate = dateRange !== null && dateRange !== void 0 && dateRange.startDate ? format(parseISO(dateRange.startDate), \"EEE, MMM d, yyyy\") : format(new Date(), \"EEE, MMM d, yyyy\");\n  useEffect(() => {\n    if (dateRange !== null && dateRange !== void 0 && dateRange.startDate && dateRange !== null && dateRange !== void 0 && dateRange.endDate) {\n      console.log(\"DayWorkReport - Dispatching getUserActivity with:\", {\n        startDate: dateRange.startDate,\n        endDate: dateRange.endDate,\n        view: 'day'\n      });\n      dispatch(ActivityActions.getUserActivity({\n        startDate: dateRange.startDate,\n        endDate: dateRange.endDate,\n        view: 'day'\n      }));\n    }\n  }, [dateRange, dispatch]);\n\n  // Debug logging\n  console.log(\"DayWorkReport - activityArr length:\", activityArr === null || activityArr === void 0 ? void 0 : activityArr.length);\n  console.log(\"DayWorkReport - dateRange:\", dateRange);\n  if ((activityArr === null || activityArr === void 0 ? void 0 : activityArr.length) > 0) {\n    console.log(\"DayWorkReport - First activity:\", activityArr[0]);\n  }\n\n  // If data is not available, show placeholder\n  if (!activityArr || activityArr.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      p: 3,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: [\"Daily Work Report \\u2013 \", displayDate]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        children: [\"No employee data available for \", displayDate, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          children: [\"Date range: \", dateRange === null || dateRange === void 0 ? void 0 : dateRange.startDate, \" to \", dateRange === null || dateRange === void 0 ? void 0 : dateRange.endDate]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          children: [\"Activity array length: \", (activityArr === null || activityArr === void 0 ? void 0 : activityArr.length) || 0]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    p: 3,\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      children: [\"Daily Work Report \\u2013 \", displayDate]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Card,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: [/*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Clock In\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Clock Out\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              colSpan: 2,\n              children: \"Entry\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              colSpan: 2,\n              children: \"Exit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Time At Work\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Productive Time\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Focus Time\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Early\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Late\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Early\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Late\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: activityArr.map((emp, i) => {\n            // Calculate progress based on productivity and focus time\n            const parseTime = timeStr => {\n              if (!timeStr || timeStr === \"-\" || timeStr === \"--\") {\n                return 0;\n              }\n              const match = timeStr.match(/(?<hours>\\d+)h\\s*(?<minutes>\\d+)m/);\n              if (!match || !match.groups) {\n                return 0;\n              }\n              const hours = parseInt(match.groups.hours, 10);\n              const minutes = parseInt(match.groups.minutes, 10);\n              return hours * 60 + minutes;\n            };\n            const totalWorkMinutes = parseTime(emp.atwork);\n            const productiveMinutes = parseTime(emp.productivitytime);\n            const focusMinutes = productiveMinutes; // Using productivity time as focus time\n\n            // Calculate progress\n            const progress = totalWorkMinutes > 0 ? Math.min(100, (productiveMinutes + focusMinutes) / totalWorkMinutes * 100) : 0;\n\n            // Determine color based on progress\n            let barColor = \"inherit\";\n            if (progress >= 90) {\n              barColor = \"success\";\n            } else if (progress >= 60) {\n              barColor = \"warning\";\n            } else if (progress > 0) {\n              barColor = \"error\";\n            }\n            return /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                    children: getInitials(emp.name || \"\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 152,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      children: emp.name || \"\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 154,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 153,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 151,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(LinearProgress, {\n                  variant: \"determinate\",\n                  value: Math.round(progress),\n                  color: barColor,\n                  sx: {\n                    height: 6,\n                    borderRadius: 4,\n                    width: \"120px\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: emp.clockin !== \"--\" ? emp.clockin : \"absent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: emp.clockout !== \"--\" ? emp.clockout : \"absent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                sx: {\n                  color: \"green\"\n                },\n                children: \"-\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                sx: {\n                  color: \"red\"\n                },\n                children: emp.entrylate || \"-\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                sx: {\n                  color: \"green\"\n                },\n                children: emp.exitearly || \"-\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                sx: {\n                  color: \"red\"\n                },\n                children: emp.exitlate || \"-\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: emp.atwork !== \"--\" ? emp.atwork : \"-\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: emp.productivitytime !== \"--\" ? emp.productivitytime : \"-\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: emp.idletime !== \"--\" ? emp.idletime : \"-\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 19\n              }, this)]\n            }, i, true, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 5\n  }, this);\n};\n_s(DayWorkReportFull, \"0uQXuJ/lhTU1kXvdhnCaK8/1Qps=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = DayWorkReportFull;\nDayWorkReportFull.propTypes = {\n  dateRange: PropTypes.shape({\n    startDate: PropTypes.string,\n    endDate: PropTypes.string\n  })\n};\nexport default DayWorkReportFull;\nvar _c;\n$RefreshReg$(_c, \"DayWorkReportFull\");", "map": {"version": 3, "names": ["React", "useEffect", "PropTypes", "useSelector", "useDispatch", "Avatar", "Box", "Card", "Typography", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "LinearProgress", "format", "parseISO", "ActivityActions", "jsxDEV", "_jsxDEV", "getInitials", "name", "split", "map", "n", "join", "formatTime", "timeStr", "minutes", "parseInt", "isNaN", "hours", "Math", "floor", "mins", "DayWorkReportFull", "date<PERSON><PERSON><PERSON>", "_s", "dispatch", "multiUserActivityArr", "state", "Activity", "activityArr", "displayDate", "startDate", "Date", "endDate", "console", "log", "view", "getUserActivity", "length", "p", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "component", "align", "colSpan", "emp", "i", "parseTime", "match", "groups", "totalWorkMinutes", "atwork", "productiveMinutes", "productivitytime", "focusMinutes", "progress", "min", "barColor", "display", "alignItems", "gap", "value", "round", "color", "sx", "height", "borderRadius", "width", "clockin", "clockout", "entrylate", "<PERSON><PERSON>ly", "exitlate", "idletime", "_c", "propTypes", "shape", "string", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/ActivityTimeline/Overview/component/DayWorkReport.jsx"], "sourcesContent": ["import React, { useEffect } from \"react\";\nimport PropTypes from \"prop-types\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport {\n  Avatar,\n  Box,\n  Card,\n  Typography,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  LinearProgress\n} from \"@mui/material\";\nimport { format, parseISO } from \"date-fns\";\nimport { ActivityActions } from \"../../../../slices/actions\";\n\nconst getInitials = (name) =>\n  name.split(\" \").map((n) => n[0]).join(\"\");\n\n const formatTime = (timeStr) => {\n  if (!timeStr || timeStr === \"--\") { return \"--\" }\n\n  const minutes = parseInt(timeStr, 10); // 👈 radix added\n  if (isNaN(minutes)) { return timeStr }\n\n  const hours = Math.floor(minutes / 60);\n  const mins = minutes % 60;\n  return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;\n};\n\n\nconst DayWorkReportFull = ({ dateRange }) => {\n  const dispatch = useDispatch();\n  const { multiUserActivityArr } = useSelector((state) => state.Activity || { multiUserActivityArr: [] });\n\n  // Use multiUserActivityArr for the ActivityTimeline components\n  const activityArr = multiUserActivityArr;\n\n  // Format the selected date for display\n  const displayDate = dateRange?.startDate ? format(parseISO(dateRange.startDate), \"EEE, MMM d, yyyy\") : format(new Date(), \"EEE, MMM d, yyyy\");\n\n  useEffect(() => {\n    if (dateRange?.startDate && dateRange?.endDate) {\n      console.log(\"DayWorkReport - Dispatching getUserActivity with:\", {\n        startDate: dateRange.startDate,\n        endDate: dateRange.endDate,\n        view: 'day'\n      });\n      dispatch(ActivityActions.getUserActivity({\n        startDate: dateRange.startDate,\n        endDate: dateRange.endDate,\n        view: 'day'\n      }));\n    }\n  }, [dateRange, dispatch]);\n\n  // Debug logging\n  console.log(\"DayWorkReport - activityArr length:\", activityArr?.length);\n  console.log(\"DayWorkReport - dateRange:\", dateRange);\n  if (activityArr?.length > 0) {\n    console.log(\"DayWorkReport - First activity:\", activityArr[0]);\n  }\n\n  // If data is not available, show placeholder\n  if (!activityArr || activityArr.length === 0) {\n    return (\n      <Box p={3}>\n        <Typography variant=\"h6\" gutterBottom>\n          Daily Work Report – {displayDate}\n        </Typography>\n        <Typography>\n          No employee data available for {displayDate}\n          <br />\n          <small>Date range: {dateRange?.startDate} to {dateRange?.endDate}</small>\n          <br />\n          <small>Activity array length: {activityArr?.length || 0}</small>\n        </Typography>\n      </Box>\n    );\n  }\n\n  return (\n    <Box p={3}>\n      <Typography variant=\"h6\" gutterBottom>\n        Daily Work Report – {displayDate}\n      </Typography>\n      <TableContainer component={Card}>\n        <Table>\n          <TableHead>\n            <TableRow>\n              <TableCell><strong>Name</strong></TableCell>\n              <TableCell></TableCell>\n              <TableCell>Clock In</TableCell>\n              <TableCell>Clock Out</TableCell>\n              <TableCell align=\"center\" colSpan={2}>Entry</TableCell>\n              <TableCell align=\"center\" colSpan={2}>Exit</TableCell>\n              <TableCell>Time At Work</TableCell>\n              <TableCell>Productive Time</TableCell>\n              <TableCell>Focus Time</TableCell>\n            </TableRow>\n            <TableRow>\n              <TableCell />\n              <TableCell />\n              <TableCell />\n              <TableCell>Early</TableCell>\n              <TableCell>Late</TableCell>\n              <TableCell>Early</TableCell>\n              <TableCell>Late</TableCell>\n              <TableCell />\n              <TableCell />\n              <TableCell />\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {activityArr.map((emp, i) => {\n              // Calculate progress based on productivity and focus time\n           const parseTime = (timeStr) => {\n  if (!timeStr || timeStr === \"-\" || timeStr === \"--\") {\n    return 0;\n  }\n\n const match = timeStr.match(/(?<hours>\\d+)h\\s*(?<minutes>\\d+)m/);\n\nif (!match || !match.groups) { return 0 }\n\nconst hours = parseInt(match.groups.hours, 10);\nconst minutes = parseInt(match.groups.minutes, 10);\nreturn (hours * 60) + minutes;\n};\n\n              \n              const totalWorkMinutes = parseTime(emp.atwork);\n              const productiveMinutes = parseTime(emp.productivitytime);\n              const focusMinutes = productiveMinutes; // Using productivity time as focus time\n              \n              // Calculate progress\n              const progress = totalWorkMinutes > 0 ? Math.min(100, ((productiveMinutes + focusMinutes) / totalWorkMinutes) * 100) : 0;\n              \n              // Determine color based on progress\n              let barColor = \"inherit\";\n              if (progress >= 90) { barColor = \"success\" }\n              else if (progress >= 60) { barColor = \"warning\" }\n              else if (progress > 0) { barColor = \"error\" }\n              \n              return (\n                <TableRow key={i}>\n                  <TableCell>\n                    <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                      <Avatar>{getInitials(emp.name || \"\")}</Avatar>\n                      <Box>\n                        <Typography>{emp.name || \"\"}</Typography>\n                      </Box>\n                    </Box>\n                  </TableCell>\n                  <TableCell>\n                    <LinearProgress\n                      variant=\"determinate\"\n                      value={Math.round(progress)}\n                      color={barColor}\n                      sx={{ height: 6, borderRadius: 4, width: \"120px\" }}\n                    />\n                  </TableCell>\n                  <TableCell>{emp.clockin !== \"--\" ? emp.clockin : \"absent\"}</TableCell>\n                  <TableCell>{emp.clockout !== \"--\" ? emp.clockout : \"absent\"}</TableCell>\n                  <TableCell sx={{ color: \"green\" }}>-</TableCell>\n                  <TableCell sx={{ color: \"red\" }}>{emp.entrylate || \"-\"}</TableCell>\n                  <TableCell sx={{ color: \"green\" }}>{emp.exitearly || \"-\"}</TableCell>\n                  <TableCell sx={{ color: \"red\" }}>{emp.exitlate || \"-\"}</TableCell>\n                  <TableCell>{emp.atwork !== \"--\" ? emp.atwork : \"-\"}</TableCell>\n                  <TableCell>{emp.productivitytime !== \"--\" ? emp.productivitytime : \"-\"}</TableCell>\n                  <TableCell>{emp.idletime !== \"--\" ? emp.idletime : \"-\"}</TableCell>\n                </TableRow>\n              );\n            })}\n          </TableBody>\n        </Table>\n      </TableContainer>\n    </Box>\n  );\n};\n\nDayWorkReportFull.propTypes = {\n  dateRange: PropTypes.shape({\n    startDate: PropTypes.string,\n    endDate: PropTypes.string\n  })\n};\n\nexport default DayWorkReportFull;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,MAAM,EACNC,GAAG,EACHC,IAAI,EACJC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,cAAc,QACT,eAAe;AACtB,SAASC,MAAM,EAAEC,QAAQ,QAAQ,UAAU;AAC3C,SAASC,eAAe,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,WAAW,GAAIC,IAAI,IACvBA,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;AAE1C,MAAMC,UAAU,GAAIC,OAAO,IAAK;EAC/B,IAAI,CAACA,OAAO,IAAIA,OAAO,KAAK,IAAI,EAAE;IAAE,OAAO,IAAI;EAAC;EAEhD,MAAMC,OAAO,GAAGC,QAAQ,CAACF,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;EACvC,IAAIG,KAAK,CAACF,OAAO,CAAC,EAAE;IAAE,OAAOD,OAAO;EAAC;EAErC,MAAMI,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACL,OAAO,GAAG,EAAE,CAAC;EACtC,MAAMM,IAAI,GAAGN,OAAO,GAAG,EAAE;EACzB,OAAOG,KAAK,GAAG,CAAC,GAAG,GAAGA,KAAK,KAAKG,IAAI,GAAG,GAAG,GAAGA,IAAI,GAAG;AACtD,CAAC;AAGD,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC3C,MAAMC,QAAQ,GAAGnC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEoC;EAAqB,CAAC,GAAGrC,WAAW,CAAEsC,KAAK,IAAKA,KAAK,CAACC,QAAQ,IAAI;IAAEF,oBAAoB,EAAE;EAAG,CAAC,CAAC;;EAEvG;EACA,MAAMG,WAAW,GAAGH,oBAAoB;;EAExC;EACA,MAAMI,WAAW,GAAGP,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEQ,SAAS,GAAG7B,MAAM,CAACC,QAAQ,CAACoB,SAAS,CAACQ,SAAS,CAAC,EAAE,kBAAkB,CAAC,GAAG7B,MAAM,CAAC,IAAI8B,IAAI,CAAC,CAAC,EAAE,kBAAkB,CAAC;EAE7I7C,SAAS,CAAC,MAAM;IACd,IAAIoC,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEQ,SAAS,IAAIR,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEU,OAAO,EAAE;MAC9CC,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE;QAC/DJ,SAAS,EAAER,SAAS,CAACQ,SAAS;QAC9BE,OAAO,EAAEV,SAAS,CAACU,OAAO;QAC1BG,IAAI,EAAE;MACR,CAAC,CAAC;MACFX,QAAQ,CAACrB,eAAe,CAACiC,eAAe,CAAC;QACvCN,SAAS,EAAER,SAAS,CAACQ,SAAS;QAC9BE,OAAO,EAAEV,SAAS,CAACU,OAAO;QAC1BG,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACb,SAAS,EAAEE,QAAQ,CAAC,CAAC;;EAEzB;EACAS,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEN,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAES,MAAM,CAAC;EACvEJ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEZ,SAAS,CAAC;EACpD,IAAI,CAAAM,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAES,MAAM,IAAG,CAAC,EAAE;IAC3BJ,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEN,WAAW,CAAC,CAAC,CAAC,CAAC;EAChE;;EAEA;EACA,IAAI,CAACA,WAAW,IAAIA,WAAW,CAACS,MAAM,KAAK,CAAC,EAAE;IAC5C,oBACEhC,OAAA,CAACd,GAAG;MAAC+C,CAAC,EAAE,CAAE;MAAAC,QAAA,gBACRlC,OAAA,CAACZ,UAAU;QAAC+C,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,GAAC,2BAChB,EAACV,WAAW;MAAA;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eACbxC,OAAA,CAACZ,UAAU;QAAA8C,QAAA,GAAC,iCACqB,EAACV,WAAW,eAC3CxB,OAAA;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNxC,OAAA;UAAAkC,QAAA,GAAO,cAAY,EAACjB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEQ,SAAS,EAAC,MAAI,EAACR,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEU,OAAO;QAAA;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACzExC,OAAA;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNxC,OAAA;UAAAkC,QAAA,GAAO,yBAAuB,EAAC,CAAAX,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAES,MAAM,KAAI,CAAC;QAAA;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,oBACExC,OAAA,CAACd,GAAG;IAAC+C,CAAC,EAAE,CAAE;IAAAC,QAAA,gBACRlC,OAAA,CAACZ,UAAU;MAAC+C,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,GAAC,2BAChB,EAACV,WAAW;IAAA;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC,eACbxC,OAAA,CAACR,cAAc;MAACiD,SAAS,EAAEtD,IAAK;MAAA+C,QAAA,eAC9BlC,OAAA,CAACX,KAAK;QAAA6C,QAAA,gBACJlC,OAAA,CAACP,SAAS;UAAAyC,QAAA,gBACRlC,OAAA,CAACN,QAAQ;YAAAwC,QAAA,gBACPlC,OAAA,CAACT,SAAS;cAAA2C,QAAA,eAAClC,OAAA;gBAAAkC,QAAA,EAAQ;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC5CxC,OAAA,CAACT,SAAS;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvBxC,OAAA,CAACT,SAAS;cAAA2C,QAAA,EAAC;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC/BxC,OAAA,CAACT,SAAS;cAAA2C,QAAA,EAAC;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAChCxC,OAAA,CAACT,SAAS;cAACmD,KAAK,EAAC,QAAQ;cAACC,OAAO,EAAE,CAAE;cAAAT,QAAA,EAAC;YAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACvDxC,OAAA,CAACT,SAAS;cAACmD,KAAK,EAAC,QAAQ;cAACC,OAAO,EAAE,CAAE;cAAAT,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACtDxC,OAAA,CAACT,SAAS;cAAA2C,QAAA,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACnCxC,OAAA,CAACT,SAAS;cAAA2C,QAAA,EAAC;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACtCxC,OAAA,CAACT,SAAS;cAAA2C,QAAA,EAAC;YAAU;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eACXxC,OAAA,CAACN,QAAQ;YAAAwC,QAAA,gBACPlC,OAAA,CAACT,SAAS;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACbxC,OAAA,CAACT,SAAS;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACbxC,OAAA,CAACT,SAAS;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACbxC,OAAA,CAACT,SAAS;cAAA2C,QAAA,EAAC;YAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC5BxC,OAAA,CAACT,SAAS;cAAA2C,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3BxC,OAAA,CAACT,SAAS;cAAA2C,QAAA,EAAC;YAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC5BxC,OAAA,CAACT,SAAS;cAAA2C,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3BxC,OAAA,CAACT,SAAS;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACbxC,OAAA,CAACT,SAAS;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACbxC,OAAA,CAACT,SAAS;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZxC,OAAA,CAACV,SAAS;UAAA4C,QAAA,EACPX,WAAW,CAACnB,GAAG,CAAC,CAACwC,GAAG,EAAEC,CAAC,KAAK;YAC3B;YACH,MAAMC,SAAS,GAAItC,OAAO,IAAK;cACxC,IAAI,CAACA,OAAO,IAAIA,OAAO,KAAK,GAAG,IAAIA,OAAO,KAAK,IAAI,EAAE;gBACnD,OAAO,CAAC;cACV;cAED,MAAMuC,KAAK,GAAGvC,OAAO,CAACuC,KAAK,CAAC,mCAAmC,CAAC;cAEjE,IAAI,CAACA,KAAK,IAAI,CAACA,KAAK,CAACC,MAAM,EAAE;gBAAE,OAAO,CAAC;cAAC;cAExC,MAAMpC,KAAK,GAAGF,QAAQ,CAACqC,KAAK,CAACC,MAAM,CAACpC,KAAK,EAAE,EAAE,CAAC;cAC9C,MAAMH,OAAO,GAAGC,QAAQ,CAACqC,KAAK,CAACC,MAAM,CAACvC,OAAO,EAAE,EAAE,CAAC;cAClD,OAAQG,KAAK,GAAG,EAAE,GAAIH,OAAO;YAC7B,CAAC;YAGa,MAAMwC,gBAAgB,GAAGH,SAAS,CAACF,GAAG,CAACM,MAAM,CAAC;YAC9C,MAAMC,iBAAiB,GAAGL,SAAS,CAACF,GAAG,CAACQ,gBAAgB,CAAC;YACzD,MAAMC,YAAY,GAAGF,iBAAiB,CAAC,CAAC;;YAExC;YACA,MAAMG,QAAQ,GAAGL,gBAAgB,GAAG,CAAC,GAAGpC,IAAI,CAAC0C,GAAG,CAAC,GAAG,EAAG,CAACJ,iBAAiB,GAAGE,YAAY,IAAIJ,gBAAgB,GAAI,GAAG,CAAC,GAAG,CAAC;;YAExH;YACA,IAAIO,QAAQ,GAAG,SAAS;YACxB,IAAIF,QAAQ,IAAI,EAAE,EAAE;cAAEE,QAAQ,GAAG,SAAS;YAAC,CAAC,MACvC,IAAIF,QAAQ,IAAI,EAAE,EAAE;cAAEE,QAAQ,GAAG,SAAS;YAAC,CAAC,MAC5C,IAAIF,QAAQ,GAAG,CAAC,EAAE;cAAEE,QAAQ,GAAG,OAAO;YAAC;YAE5C,oBACExD,OAAA,CAACN,QAAQ;cAAAwC,QAAA,gBACPlC,OAAA,CAACT,SAAS;gBAAA2C,QAAA,eACRlC,OAAA,CAACd,GAAG;kBAACuE,OAAO,EAAC,MAAM;kBAACC,UAAU,EAAC,QAAQ;kBAACC,GAAG,EAAE,CAAE;kBAAAzB,QAAA,gBAC7ClC,OAAA,CAACf,MAAM;oBAAAiD,QAAA,EAAEjC,WAAW,CAAC2C,GAAG,CAAC1C,IAAI,IAAI,EAAE;kBAAC;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,eAC9CxC,OAAA,CAACd,GAAG;oBAAAgD,QAAA,eACFlC,OAAA,CAACZ,UAAU;sBAAA8C,QAAA,EAAEU,GAAG,CAAC1C,IAAI,IAAI;oBAAE;sBAAAmC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,eACZxC,OAAA,CAACT,SAAS;gBAAA2C,QAAA,eACRlC,OAAA,CAACL,cAAc;kBACbwC,OAAO,EAAC,aAAa;kBACrByB,KAAK,EAAE/C,IAAI,CAACgD,KAAK,CAACP,QAAQ,CAAE;kBAC5BQ,KAAK,EAAEN,QAAS;kBAChBO,EAAE,EAAE;oBAAEC,MAAM,EAAE,CAAC;oBAAEC,YAAY,EAAE,CAAC;oBAAEC,KAAK,EAAE;kBAAQ;gBAAE;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZxC,OAAA,CAACT,SAAS;gBAAA2C,QAAA,EAAEU,GAAG,CAACuB,OAAO,KAAK,IAAI,GAAGvB,GAAG,CAACuB,OAAO,GAAG;cAAQ;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACtExC,OAAA,CAACT,SAAS;gBAAA2C,QAAA,EAAEU,GAAG,CAACwB,QAAQ,KAAK,IAAI,GAAGxB,GAAG,CAACwB,QAAQ,GAAG;cAAQ;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACxExC,OAAA,CAACT,SAAS;gBAACwE,EAAE,EAAE;kBAAED,KAAK,EAAE;gBAAQ,CAAE;gBAAA5B,QAAA,EAAC;cAAC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChDxC,OAAA,CAACT,SAAS;gBAACwE,EAAE,EAAE;kBAAED,KAAK,EAAE;gBAAM,CAAE;gBAAA5B,QAAA,EAAEU,GAAG,CAACyB,SAAS,IAAI;cAAG;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnExC,OAAA,CAACT,SAAS;gBAACwE,EAAE,EAAE;kBAAED,KAAK,EAAE;gBAAQ,CAAE;gBAAA5B,QAAA,EAAEU,GAAG,CAAC0B,SAAS,IAAI;cAAG;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrExC,OAAA,CAACT,SAAS;gBAACwE,EAAE,EAAE;kBAAED,KAAK,EAAE;gBAAM,CAAE;gBAAA5B,QAAA,EAAEU,GAAG,CAAC2B,QAAQ,IAAI;cAAG;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClExC,OAAA,CAACT,SAAS;gBAAA2C,QAAA,EAAEU,GAAG,CAACM,MAAM,KAAK,IAAI,GAAGN,GAAG,CAACM,MAAM,GAAG;cAAG;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC/DxC,OAAA,CAACT,SAAS;gBAAA2C,QAAA,EAAEU,GAAG,CAACQ,gBAAgB,KAAK,IAAI,GAAGR,GAAG,CAACQ,gBAAgB,GAAG;cAAG;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnFxC,OAAA,CAACT,SAAS;gBAAA2C,QAAA,EAAEU,GAAG,CAAC4B,QAAQ,KAAK,IAAI,GAAG5B,GAAG,CAAC4B,QAAQ,GAAG;cAAG;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA,GAzBtDK,CAAC;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0BN,CAAC;UAEf,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACd,CAAC;AAEV,CAAC;AAACtB,EAAA,CApJIF,iBAAiB;EAAA,QACJhC,WAAW,EACKD,WAAW;AAAA;AAAA0F,EAAA,GAFxCzD,iBAAiB;AAsJvBA,iBAAiB,CAAC0D,SAAS,GAAG;EAC5BzD,SAAS,EAAEnC,SAAS,CAAC6F,KAAK,CAAC;IACzBlD,SAAS,EAAE3C,SAAS,CAAC8F,MAAM;IAC3BjD,OAAO,EAAE7C,SAAS,CAAC8F;EACrB,CAAC;AACH,CAAC;AAED,eAAe5D,iBAAiB;AAAC,IAAAyD,EAAA;AAAAI,YAAA,CAAAJ,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}