{"ast": null, "code": "import { createSlice } from \"@reduxjs/toolkit\";\nexport const ActivitySlice = createSlice({\n  name: \"Activity\",\n  initialState: {\n    activityArr: []\n  },\n  reducers: {\n    createTodayGoal: () => {},\n    createTodayStatus: () => {},\n    getUserActivitySuccessfull: (state, action) => {\n      console.log(\"REDUCER ACTIVITY LOG - Received payload:\", action.payload);\n\n      // Handle both array and single object responses\n      if (Array.isArray(action.payload)) {\n        state.activityArr = action.payload;\n        console.log(\"REDUCER ACTIVITY LOG - Set array:\", action.payload.length, \"items\");\n      } else if (action.payload && typeof action.payload === 'object') {\n        // Single object response (for individual user history)\n        state.activityArr = [action.payload];\n        console.log(\"REDUCER ACTIVITY LOG - Set single object as array\");\n      } else {\n        state.activityArr = [];\n        console.log(\"REDUCER ACTIVITY LOG - Set empty array\");\n      }\n    },\n    getUserActivity: () => {},\n    checkOutStatusUpdate: () => {},\n    breakStartRed: () => {},\n    breakEndRed: () => {},\n    lateCheckIn: () => {},\n    earlyCheckOut: () => {},\n    idelStartRed: () => {},\n    idelEndRed: () => {},\n    productivityStatusRed: () => {},\n    overLimitBreakRed: () => {},\n    eraseActivity: (state = []) => {\n      state.activityArr = [];\n    },\n    createTimelineRequest: () => {},\n    updateTimelineRequest: () => {},\n    getTimelineRequests: () => {}\n  }\n});\nexport default ActivitySlice;", "map": {"version": 3, "names": ["createSlice", "ActivitySlice", "name", "initialState", "activityArr", "reducers", "createTodayGoal", "createTodayStatus", "getUserActivitySuccessfull", "state", "action", "console", "log", "payload", "Array", "isArray", "length", "getUserActivity", "checkOutStatusUpdate", "breakStartRed", "breakEndRed", "lateCheckIn", "earlyCheckOut", "idelStartRed", "idelEndRed", "productivityStatusRed", "overLimitBreakRed", "eraseActivity", "createTimelineRequest", "updateTimelineRequest", "getTimelineRequests"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/slices/slice/ActivitySlice.js"], "sourcesContent": ["import { createSlice } from \"@reduxjs/toolkit\";\n\n\nexport const ActivitySlice = createSlice({\n    name: \"Activity\",\n    initialState: {\n        activityArr: [],\n\n    },\n    reducers: {\n        createTodayGoal: () => { \n        },\n        createTodayStatus: () => {\n        },\n        getUserActivitySuccessfull: (state,action) => {\n            console.log(\"REDUCER ACTIVITY LOG - Received payload:\", action.payload);\n\n            // Handle both array and single object responses\n            if (Array.isArray(action.payload)) {\n                state.activityArr = action.payload;\n                console.log(\"REDUCER ACTIVITY LOG - Set array:\", action.payload.length, \"items\");\n            } else if (action.payload && typeof action.payload === 'object') {\n                // Single object response (for individual user history)\n                state.activityArr = [action.payload];\n                console.log(\"REDUCER ACTIVITY LOG - Set single object as array\");\n            } else {\n                state.activityArr = [];\n                console.log(\"REDUCER ACTIVITY LOG - Set empty array\");\n            }\n        },\n        getUserActivity: () => {\n        },\n        checkOutStatusUpdate: () => {},\n        breakStartRed: () => {},\n        breakEndRed: () => {},\n        lateCheckIn: () => {},\n        earlyCheckOut: () => {},\n        idelStartRed: () => {},\n        idelEndRed: () => {},\n        productivityStatusRed: () => {},\n        overLimitBreakRed: () => {},\n        eraseActivity: (state = []) => {\n            state.activityArr = []\n        },\n        createTimelineRequest: () => {},\n        updateTimelineRequest: () => {},\n        getTimelineRequests: () => {}\n    }\n});\n\nexport default ActivitySlice;"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAG9C,OAAO,MAAMC,aAAa,GAAGD,WAAW,CAAC;EACrCE,IAAI,EAAE,UAAU;EAChBC,YAAY,EAAE;IACVC,WAAW,EAAE;EAEjB,CAAC;EACDC,QAAQ,EAAE;IACNC,eAAe,EAAEA,CAAA,KAAM,CACvB,CAAC;IACDC,iBAAiB,EAAEA,CAAA,KAAM,CACzB,CAAC;IACDC,0BAA0B,EAAEA,CAACC,KAAK,EAACC,MAAM,KAAK;MAC1CC,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEF,MAAM,CAACG,OAAO,CAAC;;MAEvE;MACA,IAAIC,KAAK,CAACC,OAAO,CAACL,MAAM,CAACG,OAAO,CAAC,EAAE;QAC/BJ,KAAK,CAACL,WAAW,GAAGM,MAAM,CAACG,OAAO;QAClCF,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEF,MAAM,CAACG,OAAO,CAACG,MAAM,EAAE,OAAO,CAAC;MACpF,CAAC,MAAM,IAAIN,MAAM,CAACG,OAAO,IAAI,OAAOH,MAAM,CAACG,OAAO,KAAK,QAAQ,EAAE;QAC7D;QACAJ,KAAK,CAACL,WAAW,GAAG,CAACM,MAAM,CAACG,OAAO,CAAC;QACpCF,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;MACpE,CAAC,MAAM;QACHH,KAAK,CAACL,WAAW,GAAG,EAAE;QACtBO,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACzD;IACJ,CAAC;IACDK,eAAe,EAAEA,CAAA,KAAM,CACvB,CAAC;IACDC,oBAAoB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC9BC,aAAa,EAAEA,CAAA,KAAM,CAAC,CAAC;IACvBC,WAAW,EAAEA,CAAA,KAAM,CAAC,CAAC;IACrBC,WAAW,EAAEA,CAAA,KAAM,CAAC,CAAC;IACrBC,aAAa,EAAEA,CAAA,KAAM,CAAC,CAAC;IACvBC,YAAY,EAAEA,CAAA,KAAM,CAAC,CAAC;IACtBC,UAAU,EAAEA,CAAA,KAAM,CAAC,CAAC;IACpBC,qBAAqB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC/BC,iBAAiB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC3BC,aAAa,EAAEA,CAAClB,KAAK,GAAG,EAAE,KAAK;MAC3BA,KAAK,CAACL,WAAW,GAAG,EAAE;IAC1B,CAAC;IACDwB,qBAAqB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC/BC,qBAAqB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC/BC,mBAAmB,EAAEA,CAAA,KAAM,CAAC;EAChC;AACJ,CAAC,CAAC;AAEF,eAAe7B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}