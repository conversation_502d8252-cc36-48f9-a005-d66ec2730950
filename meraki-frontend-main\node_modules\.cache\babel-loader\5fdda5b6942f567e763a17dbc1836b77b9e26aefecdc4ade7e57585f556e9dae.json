{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\ActivityTimeline\\\\Overview\\\\component\\\\MonthlyWorkReport.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from \"react\";\nimport PropTypes from \"prop-types\";\nimport { Box, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography, Paper, LinearProgress, Avatar } from \"@mui/material\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { format, parseISO } from \"date-fns\";\nimport { ActivityActions } from \"../../../../slices/actions\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MonthlyWorkReport = ({\n  dateRange\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    multiUserActivityArr\n  } = useSelector(state => state.activity || {\n    multiUserActivityArr: []\n  });\n\n  // Use multiUserActivityArr for the ActivityTimeline components\n  const activityArr = multiUserActivityArr;\n\n  // Format the selected month for display\n  const displayMonth = dateRange !== null && dateRange !== void 0 && dateRange.startDate ? format(parseISO(dateRange.startDate), \"MMMM yyyy\") : format(new Date(), \"MMMM yyyy\");\n  useEffect(() => {\n    if (dateRange !== null && dateRange !== void 0 && dateRange.startDate && dateRange !== null && dateRange !== void 0 && dateRange.endDate) {\n      dispatch(ActivityActions.getUserActivity({\n        startDate: dateRange.startDate,\n        endDate: dateRange.endDate,\n        view: 'month'\n      }));\n    }\n  }, [dateRange, dispatch]);\n\n  // If data is not available, show placeholder\n  if (!activityArr || activityArr.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      p: 3,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: [\"Monthly Work Report \\u2013 \", displayMonth]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        children: \"No employee data available\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    p: 3,\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      children: [\"Monthly Work Report \\u2013 \", displayMonth]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            sx: {\n              backgroundColor: \"#f5f5f5\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Total Work Hours\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Worked Hours\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Focus Hours\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Productive Hours\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Idle + Private\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: activityArr.map((emp, idx) => {\n            // Calculate progress based on total work vs worked hours\n            const totalWork = emp.totalWork || \"0h 0m\";\n            const worked = emp.worked || \"0h 0m\";\n\n            // Determine color based on worked vs total work ratio\n            let color = \"inherit\";\n            if (emp.worked === \"--\") {\n              color = \"inherit\";\n            } else if (parseFloat(emp.worked) >= parseFloat(emp.totalWork) * 0.9) {\n              color = \"success\";\n            } else if (parseFloat(emp.worked) >= parseFloat(emp.totalWork) * 0.6) {\n              color = \"warning\";\n            } else {\n              color = \"error\";\n            }\n            return /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                    children: (emp.name || \"\")[0]\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 91,\n                    columnNumber: 23\n                  }, this), emp.name || \"\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 90,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [totalWork, /*#__PURE__*/_jsxDEV(LinearProgress, {\n                  variant: \"determinate\",\n                  value: 100,\n                  color: color,\n                  sx: {\n                    height: 6,\n                    borderRadius: 5,\n                    mt: 0.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 97,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: worked\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: emp.focus || \"--\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: emp.productive || \"--\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: emp.idle || \"--\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 19\n              }, this)]\n            }, idx, true, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n};\n_s(MonthlyWorkReport, \"0uQXuJ/lhTU1kXvdhnCaK8/1Qps=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = MonthlyWorkReport;\nMonthlyWorkReport.propTypes = {\n  dateRange: PropTypes.shape({\n    startDate: PropTypes.string,\n    endDate: PropTypes.string\n  })\n};\nexport default MonthlyWorkReport;\nvar _c;\n$RefreshReg$(_c, \"MonthlyWorkReport\");", "map": {"version": 3, "names": ["React", "useEffect", "PropTypes", "Box", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Typography", "Paper", "LinearProgress", "Avatar", "useSelector", "useDispatch", "format", "parseISO", "ActivityActions", "jsxDEV", "_jsxDEV", "MonthlyWorkReport", "date<PERSON><PERSON><PERSON>", "_s", "dispatch", "multiUserActivityArr", "state", "activity", "activityArr", "displayMonth", "startDate", "Date", "endDate", "getUserActivity", "view", "length", "p", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "component", "sx", "backgroundColor", "map", "emp", "idx", "totalWork", "worked", "color", "parseFloat", "display", "alignItems", "gap", "name", "value", "height", "borderRadius", "mt", "focus", "productive", "idle", "_c", "propTypes", "shape", "string", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/ActivityTimeline/Overview/component/MonthlyWorkReport.jsx"], "sourcesContent": ["import React, { useEffect } from \"react\";\nimport PropTypes from \"prop-types\";\nimport {\n  Box,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Typography,\n  Paper,\n  LinearProgress,\n  Avatar\n} from \"@mui/material\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { format, parseISO } from \"date-fns\";\nimport { ActivityActions } from \"../../../../slices/actions\";\n\nconst MonthlyWorkReport = ({ dateRange }) => {\n  const dispatch = useDispatch();\n  const { multiUserActivityArr } = useSelector((state) => state.activity || { multiUserActivityArr: [] });\n\n  // Use multiUserActivityArr for the ActivityTimeline components\n  const activityArr = multiUserActivityArr;\n  \n  // Format the selected month for display\n  const displayMonth = dateRange?.startDate ? format(parseISO(dateRange.startDate), \"MMMM yyyy\") : format(new Date(), \"MMMM yyyy\");\n\n  useEffect(() => {\n    if (dateRange?.startDate && dateRange?.endDate) {\n      dispatch(ActivityActions.getUserActivity({\n        startDate: dateRange.startDate,\n        endDate: dateRange.endDate,\n        view: 'month'\n      }));\n    }\n  }, [dateRange, dispatch]);\n\n  // If data is not available, show placeholder\n  if (!activityArr || activityArr.length === 0) {\n    return (\n      <Box p={3}>\n        <Typography variant=\"h6\" gutterBottom>\n          Monthly Work Report – {displayMonth}\n        </Typography>\n        <Typography>No employee data available</Typography>\n      </Box>\n    );\n  }\n\n  return (\n    <Box p={3}>\n      <Typography variant=\"h6\" gutterBottom>\n        Monthly Work Report – {displayMonth}\n      </Typography>\n      <TableContainer component={Paper}>\n        <Table>\n          <TableHead>\n            <TableRow sx={{ backgroundColor: \"#f5f5f5\" }}>\n              <TableCell>Name</TableCell>\n              <TableCell>Total Work Hours</TableCell>\n              <TableCell>Worked Hours</TableCell>\n              <TableCell>Focus Hours</TableCell>\n              <TableCell>Productive Hours</TableCell>\n              <TableCell>Idle + Private</TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {activityArr.map((emp, idx) => {\n              // Calculate progress based on total work vs worked hours\n              const totalWork = emp.totalWork || \"0h 0m\";\n              const worked = emp.worked || \"0h 0m\";\n              \n              // Determine color based on worked vs total work ratio\n              let color = \"inherit\";\n              if (emp.worked === \"--\") {\n                color = \"inherit\";\n              } else if (parseFloat(emp.worked) >= parseFloat(emp.totalWork) * 0.9) {\n                color = \"success\";\n              } else if (parseFloat(emp.worked) >= parseFloat(emp.totalWork) * 0.6) {\n                color = \"warning\";\n              } else {\n                color = \"error\";\n              }\n              \n              return (\n                <TableRow key={idx}>\n                  <TableCell>\n                    <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                      <Avatar>{(emp.name || \"\")[0]}</Avatar>\n                      {emp.name || \"\"}\n                    </Box>\n                  </TableCell>\n                  <TableCell>\n                    {totalWork}\n                    <LinearProgress\n                      variant=\"determinate\"\n                      value={100}\n                      color={color}\n                      sx={{ height: 6, borderRadius: 5, mt: 0.5 }}\n                    />\n                  </TableCell>\n                  <TableCell>{worked}</TableCell>\n                  <TableCell>{emp.focus || \"--\"}</TableCell>\n                  <TableCell>{emp.productive || \"--\"}</TableCell>\n                  <TableCell>{emp.idle || \"--\"}</TableCell>\n                </TableRow>\n              );\n            })}\n          </TableBody>\n        </Table>\n      </TableContainer>\n    </Box>\n  );\n};\n\nMonthlyWorkReport.propTypes = {\n  dateRange: PropTypes.shape({\n    startDate: PropTypes.string,\n    endDate: PropTypes.string\n  })\n};\n\nexport default MonthlyWorkReport;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,SAAS,MAAM,YAAY;AAClC,SACEC,GAAG,EACHC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVC,KAAK,EACLC,cAAc,EACdC,MAAM,QACD,eAAe;AACtB,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,MAAM,EAAEC,QAAQ,QAAQ,UAAU;AAC3C,SAASC,eAAe,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC3C,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEU;EAAqB,CAAC,GAAGX,WAAW,CAAEY,KAAK,IAAKA,KAAK,CAACC,QAAQ,IAAI;IAAEF,oBAAoB,EAAE;EAAG,CAAC,CAAC;;EAEvG;EACA,MAAMG,WAAW,GAAGH,oBAAoB;;EAExC;EACA,MAAMI,YAAY,GAAGP,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEQ,SAAS,GAAGd,MAAM,CAACC,QAAQ,CAACK,SAAS,CAACQ,SAAS,CAAC,EAAE,WAAW,CAAC,GAAGd,MAAM,CAAC,IAAIe,IAAI,CAAC,CAAC,EAAE,WAAW,CAAC;EAEhI9B,SAAS,CAAC,MAAM;IACd,IAAIqB,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEQ,SAAS,IAAIR,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEU,OAAO,EAAE;MAC9CR,QAAQ,CAACN,eAAe,CAACe,eAAe,CAAC;QACvCH,SAAS,EAAER,SAAS,CAACQ,SAAS;QAC9BE,OAAO,EAAEV,SAAS,CAACU,OAAO;QAC1BE,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACZ,SAAS,EAAEE,QAAQ,CAAC,CAAC;;EAEzB;EACA,IAAI,CAACI,WAAW,IAAIA,WAAW,CAACO,MAAM,KAAK,CAAC,EAAE;IAC5C,oBACEf,OAAA,CAACjB,GAAG;MAACiC,CAAC,EAAE,CAAE;MAAAC,QAAA,gBACRjB,OAAA,CAACV,UAAU;QAAC4B,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,GAAC,6BACd,EAACR,YAAY;MAAA;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eACbvB,OAAA,CAACV,UAAU;QAAA2B,QAAA,EAAC;MAA0B;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC;EAEV;EAEA,oBACEvB,OAAA,CAACjB,GAAG;IAACiC,CAAC,EAAE,CAAE;IAAAC,QAAA,gBACRjB,OAAA,CAACV,UAAU;MAAC4B,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,GAAC,6BACd,EAACR,YAAY;IAAA;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eACbvB,OAAA,CAACb,cAAc;MAACqC,SAAS,EAAEjC,KAAM;MAAA0B,QAAA,eAC/BjB,OAAA,CAAChB,KAAK;QAAAiC,QAAA,gBACJjB,OAAA,CAACZ,SAAS;UAAA6B,QAAA,eACRjB,OAAA,CAACX,QAAQ;YAACoC,EAAE,EAAE;cAAEC,eAAe,EAAE;YAAU,CAAE;YAAAT,QAAA,gBAC3CjB,OAAA,CAACd,SAAS;cAAA+B,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3BvB,OAAA,CAACd,SAAS;cAAA+B,QAAA,EAAC;YAAgB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACvCvB,OAAA,CAACd,SAAS;cAAA+B,QAAA,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACnCvB,OAAA,CAACd,SAAS;cAAA+B,QAAA,EAAC;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAClCvB,OAAA,CAACd,SAAS;cAAA+B,QAAA,EAAC;YAAgB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACvCvB,OAAA,CAACd,SAAS;cAAA+B,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZvB,OAAA,CAACf,SAAS;UAAAgC,QAAA,EACPT,WAAW,CAACmB,GAAG,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;YAC7B;YACA,MAAMC,SAAS,GAAGF,GAAG,CAACE,SAAS,IAAI,OAAO;YAC1C,MAAMC,MAAM,GAAGH,GAAG,CAACG,MAAM,IAAI,OAAO;;YAEpC;YACA,IAAIC,KAAK,GAAG,SAAS;YACrB,IAAIJ,GAAG,CAACG,MAAM,KAAK,IAAI,EAAE;cACvBC,KAAK,GAAG,SAAS;YACnB,CAAC,MAAM,IAAIC,UAAU,CAACL,GAAG,CAACG,MAAM,CAAC,IAAIE,UAAU,CAACL,GAAG,CAACE,SAAS,CAAC,GAAG,GAAG,EAAE;cACpEE,KAAK,GAAG,SAAS;YACnB,CAAC,MAAM,IAAIC,UAAU,CAACL,GAAG,CAACG,MAAM,CAAC,IAAIE,UAAU,CAACL,GAAG,CAACE,SAAS,CAAC,GAAG,GAAG,EAAE;cACpEE,KAAK,GAAG,SAAS;YACnB,CAAC,MAAM;cACLA,KAAK,GAAG,OAAO;YACjB;YAEA,oBACEhC,OAAA,CAACX,QAAQ;cAAA4B,QAAA,gBACPjB,OAAA,CAACd,SAAS;gBAAA+B,QAAA,eACRjB,OAAA,CAACjB,GAAG;kBAACmD,OAAO,EAAC,MAAM;kBAACC,UAAU,EAAC,QAAQ;kBAACC,GAAG,EAAE,CAAE;kBAAAnB,QAAA,gBAC7CjB,OAAA,CAACP,MAAM;oBAAAwB,QAAA,EAAE,CAACW,GAAG,CAACS,IAAI,IAAI,EAAE,EAAE,CAAC;kBAAC;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,EACrCK,GAAG,CAACS,IAAI,IAAI,EAAE;gBAAA;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,eACZvB,OAAA,CAACd,SAAS;gBAAA+B,QAAA,GACPa,SAAS,eACV9B,OAAA,CAACR,cAAc;kBACb0B,OAAO,EAAC,aAAa;kBACrBoB,KAAK,EAAE,GAAI;kBACXN,KAAK,EAAEA,KAAM;kBACbP,EAAE,EAAE;oBAAEc,MAAM,EAAE,CAAC;oBAAEC,YAAY,EAAE,CAAC;oBAAEC,EAAE,EAAE;kBAAI;gBAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZvB,OAAA,CAACd,SAAS;gBAAA+B,QAAA,EAAEc;cAAM;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC/BvB,OAAA,CAACd,SAAS;gBAAA+B,QAAA,EAAEW,GAAG,CAACc,KAAK,IAAI;cAAI;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1CvB,OAAA,CAACd,SAAS;gBAAA+B,QAAA,EAAEW,GAAG,CAACe,UAAU,IAAI;cAAI;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC/CvB,OAAA,CAACd,SAAS;gBAAA+B,QAAA,EAAEW,GAAG,CAACgB,IAAI,IAAI;cAAI;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA,GAnB5BM,GAAG;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBR,CAAC;UAEf,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACd,CAAC;AAEV,CAAC;AAACpB,EAAA,CAhGIF,iBAAiB;EAAA,QACJN,WAAW,EACKD,WAAW;AAAA;AAAAmD,EAAA,GAFxC5C,iBAAiB;AAkGvBA,iBAAiB,CAAC6C,SAAS,GAAG;EAC5B5C,SAAS,EAAEpB,SAAS,CAACiE,KAAK,CAAC;IACzBrC,SAAS,EAAE5B,SAAS,CAACkE,MAAM;IAC3BpC,OAAO,EAAE9B,SAAS,CAACkE;EACrB,CAAC;AACH,CAAC;AAED,eAAe/C,iBAAiB;AAAC,IAAA4C,EAAA;AAAAI,YAAA,CAAAJ,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}