{"ast": null, "code": "import { ActivityService } from \"services/ActivityService\";\nimport { ActivityActions, GeneralActions } from \"../slices/actions\";\nimport { all, call, put, takeLatest } from 'redux-saga/effects';\nfunction* createTodayGoal({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    console.warn(\"Create Today Goal\", payload);\n    const result = yield call(ActivityService.createTodayGoal, payload);\n    const resultHis = yield call(ActivityService.getUserActivityHistory, payload.id);\n    const data = yield resultHis.json();\n    // console.warn(\"Create Today Goal with data \",data)\n    yield put(ActivityActions.getUserActivitySuccessfull(data));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response, _err$response$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.error\n    }));\n  }\n}\nfunction* getUserActivityHistory({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n\n    // Check if payload has date range or user ID\n    if (payload && payload.startDate && payload.endDate) {\n      // This is a date range request for multiple users\n      console.log(\"ActivitySaga - Making API call with payload:\", payload);\n      const result = yield call(ActivityService.getUserActivity, payload);\n      const data = yield result.json();\n      console.log(\"ActivitySaga - API response data:\", data);\n      console.log(\"ActivitySaga - API response type:\", typeof data);\n      console.log(\"ActivitySaga - API response is array:\", Array.isArray(data));\n      yield put(ActivityActions.getUserActivitySuccessfull(data));\n    } else if (payload && payload.id) {\n      // This is a single user history request\n      console.warn(\"Get User Activity History \", payload.id);\n      const result = yield call(ActivityService.getUserActivityHistory, payload.id);\n      const data = yield result.json();\n      console.log(\"ActivitySaga - Single user API response:\", data);\n      yield put(ActivityActions.getUserActivitySuccessfull(data));\n    }\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response2, _err$response2$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.error\n    }));\n  }\n}\nfunction* updateCheckOutStatus({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    console.warn(\"payload check out \", payload);\n\n    // Call the checkout API\n    const result = yield call(ActivityService.updateCheckOutStatus, payload);\n    const data = yield result.json();\n\n    // Store the status data in the success object for the UI to access\n    if (data && data.data) {\n      yield put(GeneralActions.addSuccess({\n        action: type,\n        message: data.msg,\n        data: data.data // Include the status data\n      }));\n    }\n\n    // Update activity history\n    const result1 = yield call(ActivityService.getUserActivityHistory, payload.user);\n    const data1 = yield result1.json();\n    console.warn(\"activity history updated\", data1);\n    yield put(ActivityActions.getUserActivitySuccessfull(data1));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response3, _err$response3$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.error\n    }));\n  }\n}\nfunction* updateBreakInStatus({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    console.warn(\"payload break in \", payload);\n    const result = yield call(ActivityService.updateBreakInStatus, payload);\n    const data = yield result.json();\n    const resultHis = yield call(ActivityService.getUserActivityHistory, payload.user);\n    const data1 = yield resultHis.json();\n    console.log(\"Break in SAGA \", data1);\n    yield put(ActivityActions.getUserActivitySuccessfull(data1));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response4, _err$response4$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.error\n    }));\n  }\n}\nfunction* updateBreakOutStatus({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n\n    // Perform the break out status update\n    const result = yield call(ActivityService.updateBreakOutStatus, payload);\n    const data = yield result.json();\n\n    // Fetch the updated user activity history\n    console.warn(\"Payload Break Out \", payload);\n    const resultHis = yield call(ActivityService.getUserActivityHistory, payload.user);\n    const data1 = yield resultHis.json();\n    console.warn(\" Updated Break Out Saga \", data1);\n    // Dispatch the updated user activity history\n    yield put(ActivityActions.getUserActivitySuccessfull(data1));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response5, _err$response5$data;\n    const errorMessage = ((_err$response5 = err.response) === null || _err$response5 === void 0 ? void 0 : (_err$response5$data = _err$response5.data) === null || _err$response5$data === void 0 ? void 0 : _err$response5$data.error) || \"Unknown error occurred\";\n    yield put(GeneralActions.addError({\n      action: type,\n      message: errorMessage\n    }));\n  } finally {\n    yield put(GeneralActions.stopLoading(type));\n  }\n}\nfunction* updateTodayStatus({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    const result = yield call(ActivityService.updateTodayStatus, payload);\n    const data = yield result.json();\n    console.log(\"Today Status Payload \", payload);\n    const result1 = yield call(ActivityService.getUserActivityHistory, payload.user);\n    const data1 = yield result1.json();\n    yield put(ActivityActions.getUserActivitySuccessfull(data1));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response6, _err$response6$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response6 = err.response) === null || _err$response6 === void 0 ? void 0 : (_err$response6$data = _err$response6.data) === null || _err$response6$data === void 0 ? void 0 : _err$response6$data.error\n    }));\n  }\n}\nfunction* updateLateCheckIn({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    console.log(\"Update late check in \", payload._id);\n    const result = yield call(ActivityService.updateLateCheckInStatus, payload);\n    const data = yield result.json();\n    const resultHis = yield call(ActivityService.getUserActivityHistory, payload.user);\n    const data1 = yield resultHis.json();\n    yield put(ActivityActions.getUserActivitySuccessfull(data1));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response7, _err$response7$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response7 = err.response) === null || _err$response7 === void 0 ? void 0 : (_err$response7$data = _err$response7.data) === null || _err$response7$data === void 0 ? void 0 : _err$response7$data.error\n    }));\n  }\n}\nfunction* updateEarlyCheckOut({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    const result = yield call(ActivityService.updateEarlyCheckOutStatus, payload);\n    const data = yield result.json();\n    console.log(\"Update Early  check in \", payload._id);\n    const resultHis = yield call(ActivityService.getUserActivityHistory, payload.user);\n    const data1 = yield resultHis.json();\n    yield put(ActivityActions.getUserActivitySuccessfull(data1));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response8, _err$response8$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response8 = err.response) === null || _err$response8 === void 0 ? void 0 : (_err$response8$data = _err$response8.data) === null || _err$response8$data === void 0 ? void 0 : _err$response8$data.error\n    }));\n  }\n}\nfunction* updateIdelStart({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    const result = yield call(ActivityService.updateIdelStartStatus, payload);\n    // const data = yield result.json()\n    const resultHis = yield call(ActivityService.getUserActivityHistory, payload.user);\n    const data1 = yield resultHis.json();\n    yield put(ActivityActions.getUserActivitySuccessfull(data1));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response9, _err$response9$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response9 = err.response) === null || _err$response9 === void 0 ? void 0 : (_err$response9$data = _err$response9.data) === null || _err$response9$data === void 0 ? void 0 : _err$response9$data.error\n    }));\n  }\n}\nfunction* updateIdelEnd({\n  type,\n  payload\n}) {\n  try {\n    console.log(\"IDEL END \");\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    const result = yield call(ActivityService.updateIdelEndStatus, payload);\n    const data = yield result.json();\n    const resultHis = yield call(ActivityService.getUserActivityHistory, payload.user);\n    const data1 = yield resultHis.json();\n    yield put(ActivityActions.getUserActivitySuccessfull(data1));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response0, _err$response0$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response0 = err.response) === null || _err$response0 === void 0 ? void 0 : (_err$response0$data = _err$response0.data) === null || _err$response0$data === void 0 ? void 0 : _err$response0$data.error\n    }));\n  }\n}\nfunction* updateProductiityStatus({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    console.log(\"Productivity Status \", payload);\n    const result = yield call(ActivityService.updateProductivityStatus, payload);\n    const data = yield result.json();\n    const resultHis = yield call(ActivityService.getUserActivityHistory, payload.user);\n    const data1 = yield resultHis.json();\n    yield put(ActivityActions.getUserActivitySuccessfull(data1));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response1, _err$response1$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response1 = err.response) === null || _err$response1 === void 0 ? void 0 : (_err$response1$data = _err$response1.data) === null || _err$response1$data === void 0 ? void 0 : _err$response1$data.error\n    }));\n  }\n}\nfunction* overLimitBreakStatus({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    // console.log(\"OVER LIMIT STATUS \")\n    const result = yield call(ActivityService.updateOverLimitBreakStatus, payload);\n    const data = yield result.json();\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response10, _err$response10$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response10 = err.response) === null || _err$response10 === void 0 ? void 0 : (_err$response10$data = _err$response10.data) === null || _err$response10$data === void 0 ? void 0 : _err$response10$data.error\n    }));\n  }\n}\nexport function* ActivityWatcher() {\n  yield all([yield takeLatest(ActivityActions.createTodayGoal.type, createTodayGoal), yield takeLatest(ActivityActions.getUserActivity.type, getUserActivityHistory), yield takeLatest(ActivityActions.checkOutStatusUpdate.type, updateCheckOutStatus), yield takeLatest(ActivityActions.breakStartRed.type, updateBreakInStatus), yield takeLatest(ActivityActions.breakEndRed.type, updateBreakOutStatus), yield takeLatest(ActivityActions.createTodayStatus.type, updateTodayStatus), yield takeLatest(ActivityActions.lateCheckIn.type, updateLateCheckIn), yield takeLatest(ActivityActions.earlyCheckOut.type, updateEarlyCheckOut), yield takeLatest(ActivityActions.idelStartRed.type, updateIdelStart), yield takeLatest(ActivityActions.idelEndRed.type, updateIdelEnd), yield takeLatest(ActivityActions.overLimitBreakRed.type, overLimitBreakStatus), yield takeLatest(ActivityActions.productivityStatusRed.type, updateProductiityStatus)]);\n}\n_c = ActivityWatcher;\nvar _c;\n$RefreshReg$(_c, \"ActivityWatcher\");", "map": {"version": 3, "names": ["ActivityService", "ActivityActions", "GeneralActions", "all", "call", "put", "take<PERSON><PERSON>t", "createTodayGoal", "type", "payload", "removeError", "startLoading", "console", "warn", "result", "resultHis", "getUserActivityHistory", "id", "data", "json", "getUserActivitySuccessfull", "stopLoading", "err", "_err$response", "_err$response$data", "addError", "action", "message", "response", "error", "startDate", "endDate", "log", "getUserActivity", "Array", "isArray", "_err$response2", "_err$response2$data", "updateCheckOutStatus", "addSuccess", "msg", "result1", "user", "data1", "_err$response3", "_err$response3$data", "updateBreakInStatus", "_err$response4", "_err$response4$data", "updateBreakOutStatus", "_err$response5", "_err$response5$data", "errorMessage", "updateTodayStatus", "_err$response6", "_err$response6$data", "updateLateCheckIn", "_id", "updateLateCheckInStatus", "_err$response7", "_err$response7$data", "updateEarlyCheckOut", "updateEarlyCheckOutStatus", "_err$response8", "_err$response8$data", "updateIdelStart", "updateIdelStartStatus", "_err$response9", "_err$response9$data", "updateIdelEnd", "updateIdelEndStatus", "_err$response0", "_err$response0$data", "updateProductiityStatus", "updateProductivityStatus", "_err$response1", "_err$response1$data", "overLimitBreakStatus", "updateOverLimitBreakStatus", "_err$response10", "_err$response10$data", "ActivityWatcher", "checkOutStatusUpdate", "breakStartRed", "breakEndRed", "createTodayStatus", "lateCheckIn", "earlyCheckOut", "idelStartRed", "idelEndRed", "overLimitBreakRed", "productivityStatusRed", "_c", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/sagas/ActivitySaga.js"], "sourcesContent": ["import { ActivityService } from \"services/ActivityService\";\r\nimport {ActivityActions, GeneralActions} from \"../slices/actions\";\r\nimport {all, call, put, takeLatest} from 'redux-saga/effects'\r\n\r\n\r\nfunction *createTodayGoal({type, payload}) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        console.warn(\"Create Today Goal\",payload)\r\n        const result = yield call(ActivityService.createTodayGoal, payload);\r\n        const resultHis = yield call(ActivityService.getUserActivityHistory,payload.id);\r\n        const data = yield resultHis.json()\r\n        // console.warn(\"Create Today Goal with data \",data)\r\n        yield put(ActivityActions.getUserActivitySuccessfull(data));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n}\r\n\r\nfunction *getUserActivityHistory({type,payload}) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        \r\n        // Check if payload has date range or user ID\r\n        if (payload && payload.startDate && payload.endDate) {\r\n            // This is a date range request for multiple users\r\n            console.log(\"ActivitySaga - Making API call with payload:\", payload);\r\n            const result = yield call(ActivityService.getUserActivity, payload);\r\n            const data = yield result.json();\r\n            console.log(\"ActivitySaga - API response data:\", data);\r\n            console.log(\"ActivitySaga - API response type:\", typeof data);\r\n            console.log(\"ActivitySaga - API response is array:\", Array.isArray(data));\r\n            yield put(ActivityActions.getUserActivitySuccessfull(data));\r\n        } else if (payload && payload.id) {\r\n            // This is a single user history request\r\n            console.warn(\"Get User Activity History \", payload.id);\r\n            const result = yield call(ActivityService.getUserActivityHistory, payload.id);\r\n            const data = yield result.json();\r\n            console.log(\"ActivitySaga - Single user API response:\", data);\r\n            yield put(ActivityActions.getUserActivitySuccessfull(data));\r\n        }\r\n        \r\n        yield put(GeneralActions.stopLoading(type));\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n}\r\n\r\nfunction *updateCheckOutStatus({type,payload}) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        console.warn(\"payload check out \", payload);\r\n\r\n        // Call the checkout API\r\n        const result = yield call(ActivityService.updateCheckOutStatus, payload);\r\n        const data = yield result.json();\r\n\r\n        // Store the status data in the success object for the UI to access\r\n        if (data && data.data) {\r\n            yield put(GeneralActions.addSuccess({\r\n                action: type,\r\n                message: data.msg,\r\n                data: data.data // Include the status data\r\n            }));\r\n        }\r\n\r\n        // Update activity history\r\n        const result1 = yield call(ActivityService.getUserActivityHistory, payload.user);\r\n        const data1 = yield result1.json();\r\n        console.warn(\"activity history updated\", data1);\r\n        yield put(ActivityActions.getUserActivitySuccessfull(data1));\r\n\r\n        yield put(GeneralActions.stopLoading(type));\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n}\r\n\r\nfunction *updateBreakInStatus({type,payload}) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        console.warn(\"payload break in \",payload)\r\n        const result = yield call(ActivityService.updateBreakInStatus,payload);\r\n        const data = yield result.json()\r\n        const resultHis = yield call(ActivityService.getUserActivityHistory,payload.user);\r\n        const data1 = yield resultHis.json()\r\n        console.log(\"Break in SAGA \",data1)\r\n        yield put(ActivityActions.getUserActivitySuccessfull(data1));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n}\r\n\r\nfunction *updateBreakOutStatus({ type, payload }) {\r\n    try {\r\n      yield put(GeneralActions.removeError(type));\r\n      yield put(GeneralActions.startLoading(type));\r\n\r\n      // Perform the break out status update\r\n      const result = yield call(ActivityService.updateBreakOutStatus, payload);\r\n      const data = yield result.json();\r\n\r\n      // Fetch the updated user activity history\r\n      console.warn(\"Payload Break Out \",payload)\r\n      const resultHis = yield call(ActivityService.getUserActivityHistory,payload.user);\r\n      const data1 = yield resultHis.json();\r\n    console.warn(\" Updated Break Out Saga \",data1)\r\n      // Dispatch the updated user activity history\r\n      yield put(ActivityActions.getUserActivitySuccessfull(data1));\r\n      yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n      const errorMessage = err.response?.data?.error || \"Unknown error occurred\";\r\n      yield put(GeneralActions.addError({\r\n        action: type,\r\n        message: errorMessage\r\n      }));\r\n    } finally {\r\n      yield put(GeneralActions.stopLoading(type));\r\n    }\r\n  }\r\n\r\nfunction *updateTodayStatus({type,payload}){\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        const result = yield call(ActivityService.updateTodayStatus,payload);\r\n        const data = yield result.json()\r\n        console.log(\"Today Status Payload \",payload)\r\n        const result1 = yield call(ActivityService.getUserActivityHistory,payload.user);\r\n        const data1 = yield result1.json()\r\n         yield put(ActivityActions.getUserActivitySuccessfull(data1));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n\r\n}\r\n\r\nfunction *updateLateCheckIn({type,payload}){\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        console.log(\"Update late check in \",payload._id)\r\n        const result = yield call(ActivityService.updateLateCheckInStatus,payload);\r\n        const data = yield result.json()\r\n        const resultHis = yield call(ActivityService.getUserActivityHistory,payload.user);\r\n        const data1 = yield resultHis.json()\r\n        yield put(ActivityActions.getUserActivitySuccessfull(data1));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n\r\n}\r\n\r\nfunction *updateEarlyCheckOut({type,payload}){\r\n\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        const result = yield call(ActivityService.updateEarlyCheckOutStatus,payload);\r\n        const data = yield result.json()\r\n        console.log(\"Update Early  check in \",payload._id)\r\n        const resultHis = yield call(ActivityService.getUserActivityHistory,payload.user);\r\n        const data1 = yield resultHis.json()\r\n        yield put(ActivityActions.getUserActivitySuccessfull(data1));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n\r\n}\r\n\r\nfunction *updateIdelStart({type,payload}) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        const result = yield call(ActivityService.updateIdelStartStatus,payload);\r\n        // const data = yield result.json()\r\n        const resultHis = yield call(ActivityService.getUserActivityHistory,payload.user);\r\n        const data1 = yield resultHis.json()\r\n        yield put(ActivityActions.getUserActivitySuccessfull(data1));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n}\r\n\r\nfunction *updateIdelEnd({type,payload}) {\r\n    try {\r\n        console.log(\"IDEL END \")\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        const result = yield call(ActivityService.updateIdelEndStatus,payload);\r\n        const data = yield result.json()\r\n        const resultHis = yield call(ActivityService.getUserActivityHistory,payload.user);\r\n        const data1 = yield resultHis.json()\r\n        yield put(ActivityActions.getUserActivitySuccessfull(data1));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n\r\n}\r\n\r\nfunction *updateProductiityStatus({type,payload}) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n         console.log(\"Productivity Status \",payload)\r\n        const result = yield call(ActivityService.updateProductivityStatus,payload);\r\n        const data = yield result.json()\r\n\r\n        const resultHis = yield call(ActivityService.getUserActivityHistory,payload.user);\r\n        const data1 = yield resultHis.json()\r\n        yield put(ActivityActions.getUserActivitySuccessfull(data1));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n}\r\n\r\nfunction *overLimitBreakStatus({type,payload}) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        // console.log(\"OVER LIMIT STATUS \")\r\n        const result = yield call(ActivityService.updateOverLimitBreakStatus,payload);\r\n        const data = yield result.json()\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n}\r\n\r\n\r\n\r\nexport function *ActivityWatcher() {\r\n    yield all([\r\n        yield takeLatest(ActivityActions.createTodayGoal.type, createTodayGoal),\r\n        yield takeLatest(ActivityActions.getUserActivity.type, getUserActivityHistory),\r\n        yield takeLatest(ActivityActions.checkOutStatusUpdate.type, updateCheckOutStatus),\r\n        yield takeLatest(ActivityActions.breakStartRed.type, updateBreakInStatus),\r\n        yield takeLatest(ActivityActions.breakEndRed.type, updateBreakOutStatus),\r\n        yield takeLatest(ActivityActions.createTodayStatus.type,updateTodayStatus),\r\n        yield takeLatest(ActivityActions.lateCheckIn.type,updateLateCheckIn),\r\n        yield takeLatest(ActivityActions.earlyCheckOut.type,updateEarlyCheckOut),\r\n        yield takeLatest(ActivityActions.idelStartRed.type,updateIdelStart),\r\n        yield takeLatest(ActivityActions.idelEndRed.type,updateIdelEnd),\r\n        yield takeLatest(ActivityActions.overLimitBreakRed.type,overLimitBreakStatus),\r\n        yield takeLatest(ActivityActions.productivityStatusRed.type,updateProductiityStatus)\r\n ]);\r\n}"], "mappings": "AAAA,SAASA,eAAe,QAAQ,0BAA0B;AAC1D,SAAQC,eAAe,EAAEC,cAAc,QAAO,mBAAmB;AACjE,SAAQC,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,UAAU,QAAO,oBAAoB;AAG7D,UAAUC,eAAeA,CAAC;EAACC,IAAI;EAAEC;AAAO,CAAC,EAAE;EACvC,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5CI,OAAO,CAACC,IAAI,CAAC,mBAAmB,EAACJ,OAAO,CAAC;IACzC,MAAMK,MAAM,GAAG,MAAMV,IAAI,CAACJ,eAAe,CAACO,eAAe,EAAEE,OAAO,CAAC;IACnE,MAAMM,SAAS,GAAG,MAAMX,IAAI,CAACJ,eAAe,CAACgB,sBAAsB,EAACP,OAAO,CAACQ,EAAE,CAAC;IAC/E,MAAMC,IAAI,GAAG,MAAMH,SAAS,CAACI,IAAI,CAAC,CAAC;IACnC;IACA,MAAMd,GAAG,CAACJ,eAAe,CAACmB,0BAA0B,CAACF,IAAI,CAAC,CAAC;IAC3D,MAAMb,GAAG,CAACH,cAAc,CAACmB,WAAW,CAACb,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOc,GAAG,EAAE;IAAA,IAAAC,aAAA,EAAAC,kBAAA;IACV,MAAMnB,GAAG,CAACH,cAAc,CAACmB,WAAW,CAACb,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACuB,QAAQ,CAAC;MAC9BC,MAAM,EAAElB,IAAI;MACZmB,OAAO,GAAAJ,aAAA,GAAED,GAAG,CAACM,QAAQ,cAAAL,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcL,IAAI,cAAAM,kBAAA,uBAAlBA,kBAAA,CAAoBK;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,UAAUb,sBAAsBA,CAAC;EAACR,IAAI;EAACC;AAAO,CAAC,EAAE;EAC7C,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;;IAE5C;IACA,IAAIC,OAAO,IAAIA,OAAO,CAACqB,SAAS,IAAIrB,OAAO,CAACsB,OAAO,EAAE;MACjD;MACAnB,OAAO,CAACoB,GAAG,CAAC,8CAA8C,EAAEvB,OAAO,CAAC;MACpE,MAAMK,MAAM,GAAG,MAAMV,IAAI,CAACJ,eAAe,CAACiC,eAAe,EAAExB,OAAO,CAAC;MACnE,MAAMS,IAAI,GAAG,MAAMJ,MAAM,CAACK,IAAI,CAAC,CAAC;MAChCP,OAAO,CAACoB,GAAG,CAAC,mCAAmC,EAAEd,IAAI,CAAC;MACtDN,OAAO,CAACoB,GAAG,CAAC,mCAAmC,EAAE,OAAOd,IAAI,CAAC;MAC7DN,OAAO,CAACoB,GAAG,CAAC,uCAAuC,EAAEE,KAAK,CAACC,OAAO,CAACjB,IAAI,CAAC,CAAC;MACzE,MAAMb,GAAG,CAACJ,eAAe,CAACmB,0BAA0B,CAACF,IAAI,CAAC,CAAC;IAC/D,CAAC,MAAM,IAAIT,OAAO,IAAIA,OAAO,CAACQ,EAAE,EAAE;MAC9B;MACAL,OAAO,CAACC,IAAI,CAAC,4BAA4B,EAAEJ,OAAO,CAACQ,EAAE,CAAC;MACtD,MAAMH,MAAM,GAAG,MAAMV,IAAI,CAACJ,eAAe,CAACgB,sBAAsB,EAAEP,OAAO,CAACQ,EAAE,CAAC;MAC7E,MAAMC,IAAI,GAAG,MAAMJ,MAAM,CAACK,IAAI,CAAC,CAAC;MAChCP,OAAO,CAACoB,GAAG,CAAC,0CAA0C,EAAEd,IAAI,CAAC;MAC7D,MAAMb,GAAG,CAACJ,eAAe,CAACmB,0BAA0B,CAACF,IAAI,CAAC,CAAC;IAC/D;IAEA,MAAMb,GAAG,CAACH,cAAc,CAACmB,WAAW,CAACb,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOc,GAAG,EAAE;IAAA,IAAAc,cAAA,EAAAC,mBAAA;IACV,MAAMhC,GAAG,CAACH,cAAc,CAACmB,WAAW,CAACb,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACuB,QAAQ,CAAC;MAC9BC,MAAM,EAAElB,IAAI;MACZmB,OAAO,GAAAS,cAAA,GAAEd,GAAG,CAACM,QAAQ,cAAAQ,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAclB,IAAI,cAAAmB,mBAAA,uBAAlBA,mBAAA,CAAoBR;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,UAAUS,oBAAoBA,CAAC;EAAC9B,IAAI;EAACC;AAAO,CAAC,EAAE;EAC3C,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5CI,OAAO,CAACC,IAAI,CAAC,oBAAoB,EAAEJ,OAAO,CAAC;;IAE3C;IACA,MAAMK,MAAM,GAAG,MAAMV,IAAI,CAACJ,eAAe,CAACsC,oBAAoB,EAAE7B,OAAO,CAAC;IACxE,MAAMS,IAAI,GAAG,MAAMJ,MAAM,CAACK,IAAI,CAAC,CAAC;;IAEhC;IACA,IAAID,IAAI,IAAIA,IAAI,CAACA,IAAI,EAAE;MACnB,MAAMb,GAAG,CAACH,cAAc,CAACqC,UAAU,CAAC;QAChCb,MAAM,EAAElB,IAAI;QACZmB,OAAO,EAAET,IAAI,CAACsB,GAAG;QACjBtB,IAAI,EAAEA,IAAI,CAACA,IAAI,CAAC;MACpB,CAAC,CAAC,CAAC;IACP;;IAEA;IACA,MAAMuB,OAAO,GAAG,MAAMrC,IAAI,CAACJ,eAAe,CAACgB,sBAAsB,EAAEP,OAAO,CAACiC,IAAI,CAAC;IAChF,MAAMC,KAAK,GAAG,MAAMF,OAAO,CAACtB,IAAI,CAAC,CAAC;IAClCP,OAAO,CAACC,IAAI,CAAC,0BAA0B,EAAE8B,KAAK,CAAC;IAC/C,MAAMtC,GAAG,CAACJ,eAAe,CAACmB,0BAA0B,CAACuB,KAAK,CAAC,CAAC;IAE5D,MAAMtC,GAAG,CAACH,cAAc,CAACmB,WAAW,CAACb,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOc,GAAG,EAAE;IAAA,IAAAsB,cAAA,EAAAC,mBAAA;IACV,MAAMxC,GAAG,CAACH,cAAc,CAACmB,WAAW,CAACb,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACuB,QAAQ,CAAC;MAC9BC,MAAM,EAAElB,IAAI;MACZmB,OAAO,GAAAiB,cAAA,GAAEtB,GAAG,CAACM,QAAQ,cAAAgB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc1B,IAAI,cAAA2B,mBAAA,uBAAlBA,mBAAA,CAAoBhB;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,UAAUiB,mBAAmBA,CAAC;EAACtC,IAAI;EAACC;AAAO,CAAC,EAAE;EAC1C,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5CI,OAAO,CAACC,IAAI,CAAC,mBAAmB,EAACJ,OAAO,CAAC;IACzC,MAAMK,MAAM,GAAG,MAAMV,IAAI,CAACJ,eAAe,CAAC8C,mBAAmB,EAACrC,OAAO,CAAC;IACtE,MAAMS,IAAI,GAAG,MAAMJ,MAAM,CAACK,IAAI,CAAC,CAAC;IAChC,MAAMJ,SAAS,GAAG,MAAMX,IAAI,CAACJ,eAAe,CAACgB,sBAAsB,EAACP,OAAO,CAACiC,IAAI,CAAC;IACjF,MAAMC,KAAK,GAAG,MAAM5B,SAAS,CAACI,IAAI,CAAC,CAAC;IACpCP,OAAO,CAACoB,GAAG,CAAC,gBAAgB,EAACW,KAAK,CAAC;IACnC,MAAMtC,GAAG,CAACJ,eAAe,CAACmB,0BAA0B,CAACuB,KAAK,CAAC,CAAC;IAC5D,MAAMtC,GAAG,CAACH,cAAc,CAACmB,WAAW,CAACb,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOc,GAAG,EAAE;IAAA,IAAAyB,cAAA,EAAAC,mBAAA;IACV,MAAM3C,GAAG,CAACH,cAAc,CAACmB,WAAW,CAACb,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACuB,QAAQ,CAAC;MAC9BC,MAAM,EAAElB,IAAI;MACZmB,OAAO,GAAAoB,cAAA,GAAEzB,GAAG,CAACM,QAAQ,cAAAmB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc7B,IAAI,cAAA8B,mBAAA,uBAAlBA,mBAAA,CAAoBnB;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,UAAUoB,oBAAoBA,CAAC;EAAEzC,IAAI;EAAEC;AAAQ,CAAC,EAAE;EAC9C,IAAI;IACF,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;;IAE5C;IACA,MAAMM,MAAM,GAAG,MAAMV,IAAI,CAACJ,eAAe,CAACiD,oBAAoB,EAAExC,OAAO,CAAC;IACxE,MAAMS,IAAI,GAAG,MAAMJ,MAAM,CAACK,IAAI,CAAC,CAAC;;IAEhC;IACAP,OAAO,CAACC,IAAI,CAAC,oBAAoB,EAACJ,OAAO,CAAC;IAC1C,MAAMM,SAAS,GAAG,MAAMX,IAAI,CAACJ,eAAe,CAACgB,sBAAsB,EAACP,OAAO,CAACiC,IAAI,CAAC;IACjF,MAAMC,KAAK,GAAG,MAAM5B,SAAS,CAACI,IAAI,CAAC,CAAC;IACtCP,OAAO,CAACC,IAAI,CAAC,0BAA0B,EAAC8B,KAAK,CAAC;IAC5C;IACA,MAAMtC,GAAG,CAACJ,eAAe,CAACmB,0BAA0B,CAACuB,KAAK,CAAC,CAAC;IAC5D,MAAMtC,GAAG,CAACH,cAAc,CAACmB,WAAW,CAACb,IAAI,CAAC,CAAC;EAC7C,CAAC,CAAC,OAAOc,GAAG,EAAE;IAAA,IAAA4B,cAAA,EAAAC,mBAAA;IACZ,MAAMC,YAAY,GAAG,EAAAF,cAAA,GAAA5B,GAAG,CAACM,QAAQ,cAAAsB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAchC,IAAI,cAAAiC,mBAAA,uBAAlBA,mBAAA,CAAoBtB,KAAK,KAAI,wBAAwB;IAC1E,MAAMxB,GAAG,CAACH,cAAc,CAACuB,QAAQ,CAAC;MAChCC,MAAM,EAAElB,IAAI;MACZmB,OAAO,EAAEyB;IACX,CAAC,CAAC,CAAC;EACL,CAAC,SAAS;IACR,MAAM/C,GAAG,CAACH,cAAc,CAACmB,WAAW,CAACb,IAAI,CAAC,CAAC;EAC7C;AACF;AAEF,UAAU6C,iBAAiBA,CAAC;EAAC7C,IAAI;EAACC;AAAO,CAAC,EAAC;EACvC,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5C,MAAMM,MAAM,GAAG,MAAMV,IAAI,CAACJ,eAAe,CAACqD,iBAAiB,EAAC5C,OAAO,CAAC;IACpE,MAAMS,IAAI,GAAG,MAAMJ,MAAM,CAACK,IAAI,CAAC,CAAC;IAChCP,OAAO,CAACoB,GAAG,CAAC,uBAAuB,EAACvB,OAAO,CAAC;IAC5C,MAAMgC,OAAO,GAAG,MAAMrC,IAAI,CAACJ,eAAe,CAACgB,sBAAsB,EAACP,OAAO,CAACiC,IAAI,CAAC;IAC/E,MAAMC,KAAK,GAAG,MAAMF,OAAO,CAACtB,IAAI,CAAC,CAAC;IACjC,MAAMd,GAAG,CAACJ,eAAe,CAACmB,0BAA0B,CAACuB,KAAK,CAAC,CAAC;IAC7D,MAAMtC,GAAG,CAACH,cAAc,CAACmB,WAAW,CAACb,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOc,GAAG,EAAE;IAAA,IAAAgC,cAAA,EAAAC,mBAAA;IACV,MAAMlD,GAAG,CAACH,cAAc,CAACmB,WAAW,CAACb,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACuB,QAAQ,CAAC;MAC9BC,MAAM,EAAElB,IAAI;MACZmB,OAAO,GAAA2B,cAAA,GAAEhC,GAAG,CAACM,QAAQ,cAAA0B,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcpC,IAAI,cAAAqC,mBAAA,uBAAlBA,mBAAA,CAAoB1B;IACjC,CAAC,CAAC,CAAC;EACP;AAEJ;AAEA,UAAU2B,iBAAiBA,CAAC;EAAChD,IAAI;EAACC;AAAO,CAAC,EAAC;EACvC,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5CI,OAAO,CAACoB,GAAG,CAAC,uBAAuB,EAACvB,OAAO,CAACgD,GAAG,CAAC;IAChD,MAAM3C,MAAM,GAAG,MAAMV,IAAI,CAACJ,eAAe,CAAC0D,uBAAuB,EAACjD,OAAO,CAAC;IAC1E,MAAMS,IAAI,GAAG,MAAMJ,MAAM,CAACK,IAAI,CAAC,CAAC;IAChC,MAAMJ,SAAS,GAAG,MAAMX,IAAI,CAACJ,eAAe,CAACgB,sBAAsB,EAACP,OAAO,CAACiC,IAAI,CAAC;IACjF,MAAMC,KAAK,GAAG,MAAM5B,SAAS,CAACI,IAAI,CAAC,CAAC;IACpC,MAAMd,GAAG,CAACJ,eAAe,CAACmB,0BAA0B,CAACuB,KAAK,CAAC,CAAC;IAC5D,MAAMtC,GAAG,CAACH,cAAc,CAACmB,WAAW,CAACb,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOc,GAAG,EAAE;IAAA,IAAAqC,cAAA,EAAAC,mBAAA;IACV,MAAMvD,GAAG,CAACH,cAAc,CAACmB,WAAW,CAACb,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACuB,QAAQ,CAAC;MAC9BC,MAAM,EAAElB,IAAI;MACZmB,OAAO,GAAAgC,cAAA,GAAErC,GAAG,CAACM,QAAQ,cAAA+B,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAczC,IAAI,cAAA0C,mBAAA,uBAAlBA,mBAAA,CAAoB/B;IACjC,CAAC,CAAC,CAAC;EACP;AAEJ;AAEA,UAAUgC,mBAAmBA,CAAC;EAACrD,IAAI;EAACC;AAAO,CAAC,EAAC;EAEzC,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5C,MAAMM,MAAM,GAAG,MAAMV,IAAI,CAACJ,eAAe,CAAC8D,yBAAyB,EAACrD,OAAO,CAAC;IAC5E,MAAMS,IAAI,GAAG,MAAMJ,MAAM,CAACK,IAAI,CAAC,CAAC;IAChCP,OAAO,CAACoB,GAAG,CAAC,yBAAyB,EAACvB,OAAO,CAACgD,GAAG,CAAC;IAClD,MAAM1C,SAAS,GAAG,MAAMX,IAAI,CAACJ,eAAe,CAACgB,sBAAsB,EAACP,OAAO,CAACiC,IAAI,CAAC;IACjF,MAAMC,KAAK,GAAG,MAAM5B,SAAS,CAACI,IAAI,CAAC,CAAC;IACpC,MAAMd,GAAG,CAACJ,eAAe,CAACmB,0BAA0B,CAACuB,KAAK,CAAC,CAAC;IAC5D,MAAMtC,GAAG,CAACH,cAAc,CAACmB,WAAW,CAACb,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOc,GAAG,EAAE;IAAA,IAAAyC,cAAA,EAAAC,mBAAA;IACV,MAAM3D,GAAG,CAACH,cAAc,CAACmB,WAAW,CAACb,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACuB,QAAQ,CAAC;MAC9BC,MAAM,EAAElB,IAAI;MACZmB,OAAO,GAAAoC,cAAA,GAAEzC,GAAG,CAACM,QAAQ,cAAAmC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc7C,IAAI,cAAA8C,mBAAA,uBAAlBA,mBAAA,CAAoBnC;IACjC,CAAC,CAAC,CAAC;EACP;AAEJ;AAEA,UAAUoC,eAAeA,CAAC;EAACzD,IAAI;EAACC;AAAO,CAAC,EAAE;EACtC,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5C,MAAMM,MAAM,GAAG,MAAMV,IAAI,CAACJ,eAAe,CAACkE,qBAAqB,EAACzD,OAAO,CAAC;IACxE;IACA,MAAMM,SAAS,GAAG,MAAMX,IAAI,CAACJ,eAAe,CAACgB,sBAAsB,EAACP,OAAO,CAACiC,IAAI,CAAC;IACjF,MAAMC,KAAK,GAAG,MAAM5B,SAAS,CAACI,IAAI,CAAC,CAAC;IACpC,MAAMd,GAAG,CAACJ,eAAe,CAACmB,0BAA0B,CAACuB,KAAK,CAAC,CAAC;IAC5D,MAAMtC,GAAG,CAACH,cAAc,CAACmB,WAAW,CAACb,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOc,GAAG,EAAE;IAAA,IAAA6C,cAAA,EAAAC,mBAAA;IACV,MAAM/D,GAAG,CAACH,cAAc,CAACmB,WAAW,CAACb,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACuB,QAAQ,CAAC;MAC9BC,MAAM,EAAElB,IAAI;MACZmB,OAAO,GAAAwC,cAAA,GAAE7C,GAAG,CAACM,QAAQ,cAAAuC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcjD,IAAI,cAAAkD,mBAAA,uBAAlBA,mBAAA,CAAoBvC;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,UAAUwC,aAAaA,CAAC;EAAC7D,IAAI;EAACC;AAAO,CAAC,EAAE;EACpC,IAAI;IACAG,OAAO,CAACoB,GAAG,CAAC,WAAW,CAAC;IACxB,MAAM3B,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5C,MAAMM,MAAM,GAAG,MAAMV,IAAI,CAACJ,eAAe,CAACsE,mBAAmB,EAAC7D,OAAO,CAAC;IACtE,MAAMS,IAAI,GAAG,MAAMJ,MAAM,CAACK,IAAI,CAAC,CAAC;IAChC,MAAMJ,SAAS,GAAG,MAAMX,IAAI,CAACJ,eAAe,CAACgB,sBAAsB,EAACP,OAAO,CAACiC,IAAI,CAAC;IACjF,MAAMC,KAAK,GAAG,MAAM5B,SAAS,CAACI,IAAI,CAAC,CAAC;IACpC,MAAMd,GAAG,CAACJ,eAAe,CAACmB,0BAA0B,CAACuB,KAAK,CAAC,CAAC;IAC5D,MAAMtC,GAAG,CAACH,cAAc,CAACmB,WAAW,CAACb,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOc,GAAG,EAAE;IAAA,IAAAiD,cAAA,EAAAC,mBAAA;IACV,MAAMnE,GAAG,CAACH,cAAc,CAACmB,WAAW,CAACb,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACuB,QAAQ,CAAC;MAC9BC,MAAM,EAAElB,IAAI;MACZmB,OAAO,GAAA4C,cAAA,GAAEjD,GAAG,CAACM,QAAQ,cAAA2C,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcrD,IAAI,cAAAsD,mBAAA,uBAAlBA,mBAAA,CAAoB3C;IACjC,CAAC,CAAC,CAAC;EACP;AAEJ;AAEA,UAAU4C,uBAAuBA,CAAC;EAACjE,IAAI;EAACC;AAAO,CAAC,EAAE;EAC9C,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC3CI,OAAO,CAACoB,GAAG,CAAC,sBAAsB,EAACvB,OAAO,CAAC;IAC5C,MAAMK,MAAM,GAAG,MAAMV,IAAI,CAACJ,eAAe,CAAC0E,wBAAwB,EAACjE,OAAO,CAAC;IAC3E,MAAMS,IAAI,GAAG,MAAMJ,MAAM,CAACK,IAAI,CAAC,CAAC;IAEhC,MAAMJ,SAAS,GAAG,MAAMX,IAAI,CAACJ,eAAe,CAACgB,sBAAsB,EAACP,OAAO,CAACiC,IAAI,CAAC;IACjF,MAAMC,KAAK,GAAG,MAAM5B,SAAS,CAACI,IAAI,CAAC,CAAC;IACpC,MAAMd,GAAG,CAACJ,eAAe,CAACmB,0BAA0B,CAACuB,KAAK,CAAC,CAAC;IAC5D,MAAMtC,GAAG,CAACH,cAAc,CAACmB,WAAW,CAACb,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOc,GAAG,EAAE;IAAA,IAAAqD,cAAA,EAAAC,mBAAA;IACV,MAAMvE,GAAG,CAACH,cAAc,CAACmB,WAAW,CAACb,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACuB,QAAQ,CAAC;MAC9BC,MAAM,EAAElB,IAAI;MACZmB,OAAO,GAAAgD,cAAA,GAAErD,GAAG,CAACM,QAAQ,cAAA+C,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAczD,IAAI,cAAA0D,mBAAA,uBAAlBA,mBAAA,CAAoB/C;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,UAAUgD,oBAAoBA,CAAC;EAACrE,IAAI;EAACC;AAAO,CAAC,EAAE;EAC3C,IAAI;IACA,MAAMJ,GAAG,CAACH,cAAc,CAACQ,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACS,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5C;IACA,MAAMM,MAAM,GAAG,MAAMV,IAAI,CAACJ,eAAe,CAAC8E,0BAA0B,EAACrE,OAAO,CAAC;IAC7E,MAAMS,IAAI,GAAG,MAAMJ,MAAM,CAACK,IAAI,CAAC,CAAC;IAChC,MAAMd,GAAG,CAACH,cAAc,CAACmB,WAAW,CAACb,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOc,GAAG,EAAE;IAAA,IAAAyD,eAAA,EAAAC,oBAAA;IACV,MAAM3E,GAAG,CAACH,cAAc,CAACmB,WAAW,CAACb,IAAI,CAAC,CAAC;IAC3C,MAAMH,GAAG,CAACH,cAAc,CAACuB,QAAQ,CAAC;MAC9BC,MAAM,EAAElB,IAAI;MACZmB,OAAO,GAAAoD,eAAA,GAAEzD,GAAG,CAACM,QAAQ,cAAAmD,eAAA,wBAAAC,oBAAA,GAAZD,eAAA,CAAc7D,IAAI,cAAA8D,oBAAA,uBAAlBA,oBAAA,CAAoBnD;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AAIA,OAAO,UAAUoD,eAAeA,CAAA,EAAG;EAC/B,MAAM9E,GAAG,CAAC,CACN,MAAMG,UAAU,CAACL,eAAe,CAACM,eAAe,CAACC,IAAI,EAAED,eAAe,CAAC,EACvE,MAAMD,UAAU,CAACL,eAAe,CAACgC,eAAe,CAACzB,IAAI,EAAEQ,sBAAsB,CAAC,EAC9E,MAAMV,UAAU,CAACL,eAAe,CAACiF,oBAAoB,CAAC1E,IAAI,EAAE8B,oBAAoB,CAAC,EACjF,MAAMhC,UAAU,CAACL,eAAe,CAACkF,aAAa,CAAC3E,IAAI,EAAEsC,mBAAmB,CAAC,EACzE,MAAMxC,UAAU,CAACL,eAAe,CAACmF,WAAW,CAAC5E,IAAI,EAAEyC,oBAAoB,CAAC,EACxE,MAAM3C,UAAU,CAACL,eAAe,CAACoF,iBAAiB,CAAC7E,IAAI,EAAC6C,iBAAiB,CAAC,EAC1E,MAAM/C,UAAU,CAACL,eAAe,CAACqF,WAAW,CAAC9E,IAAI,EAACgD,iBAAiB,CAAC,EACpE,MAAMlD,UAAU,CAACL,eAAe,CAACsF,aAAa,CAAC/E,IAAI,EAACqD,mBAAmB,CAAC,EACxE,MAAMvD,UAAU,CAACL,eAAe,CAACuF,YAAY,CAAChF,IAAI,EAACyD,eAAe,CAAC,EACnE,MAAM3D,UAAU,CAACL,eAAe,CAACwF,UAAU,CAACjF,IAAI,EAAC6D,aAAa,CAAC,EAC/D,MAAM/D,UAAU,CAACL,eAAe,CAACyF,iBAAiB,CAAClF,IAAI,EAACqE,oBAAoB,CAAC,EAC7E,MAAMvE,UAAU,CAACL,eAAe,CAAC0F,qBAAqB,CAACnF,IAAI,EAACiE,uBAAuB,CAAC,CAC1F,CAAC;AACH;AAACmB,EAAA,GAfgBX,eAAe;AAAA,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}