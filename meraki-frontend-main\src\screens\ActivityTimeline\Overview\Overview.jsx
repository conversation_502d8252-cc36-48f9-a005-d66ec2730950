import React, { useState, useEffect } from 'react';
import { Box, Button } from '@mui/material';
import MonthlyWorkReport from './component/MonthlyWorkReport';
import DayWorkReport from './component/DayWorkReport';
import WeekWorkReport from './component/WeekWorkReport';
import DayPicker from './TimePickers/DayPicker';
import WeeklyPicker from './TimePickers/WeeklyPicker';
import MonthPicker from './TimePickers/MonthPicker';
import { useDispatch } from 'react-redux';
import { ActivityActions } from '../../../slices/actions';
import dayjs from 'dayjs';

function Overview() {
  const dispatch = useDispatch();
  const [viewOption, setViewOption] = useState('Day');

  // Set default date range based on view option
  const getDefaultDateRange = (view) => {
    const today = dayjs();
    switch(view) {
      case 'Day':
        return {
          startDate: today.format('YYYY-MM-DD'),
          endDate: today.format('YYYY-MM-DD')
        };
      case 'Week':
        return {
          startDate: today.startOf('week').format('YYYY-MM-DD'),
          endDate: today.endOf('week').format('YYYY-MM-DD')
        };
      case 'Month':
        return {
          startDate: today.startOf('month').format('YYYY-MM-DD'),
          endDate: today.endOf('month').format('YYYY-MM-DD')
        };
      default:
        return {
          startDate: today.format('YYYY-MM-DD'),
          endDate: today.format('YYYY-MM-DD')
        };
    }
  };

  const [dateRange, setDateRange] = useState(getDefaultDateRange('Day'));

  // Handle date changes from pickers
  const handleDateChange = (newDateRange) => {
    console.log("Overview - Date range changed:", newDateRange);
    setDateRange(newDateRange);
  };

  // Update date range when view option changes
  useEffect(() => {
    const newDateRange = getDefaultDateRange(viewOption);
    console.log("Overview - View changed to:", viewOption, "New date range:", newDateRange);
    setDateRange(newDateRange);
  }, [viewOption]);

  // Fetch activity data when date range or view changes
  useEffect(() => {
    if (dateRange.startDate && dateRange.endDate) {
      console.log("Overview - Fetching data for:", {
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
        view: viewOption.toLowerCase()
      });
      dispatch(ActivityActions.getUserActivity({
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
        view: viewOption.toLowerCase()
      }));
    }
  }, [dateRange, viewOption, dispatch]);

  // Test function to load data for June 11, 2025 (where we know there's activity)
  const testLoadJune11Data = () => {
    const testDateRange = {
      startDate: '2025-06-11',
      endDate: '2025-06-11'
    };
    console.log("Testing with June 11, 2025 data:", testDateRange);
    setDateRange(testDateRange);
  };

  return (
    <>
      <h1>Overview</h1>

      {/* Test button - remove this after debugging */}
      <Box sx={{ mb: 2 }}>
        <Button
          variant="outlined"
          onClick={testLoadJune11Data}
          sx={{ mr: 2 }}
        >
          Test Load June 11, 2025 Data
        </Button>
        <Button
          variant="outlined"
          onClick={() => console.log("Current state:", { dateRange, viewOption })}
        >
          Debug Current State
        </Button>
      </Box>

      {/* View options and date picker */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 3,
          position: 'relative',
        }}
      >
        {/* Day/Week/Month tabs */}
        <Box
          sx={{
            display: 'flex',
            borderRadius: '4px',
            overflow: 'hidden',
            border: '1px solid #e0e0e0',
          }}
        >
          {['Day', 'Week', 'Month'].map((option) => (
            <Button
              key={option}
              onClick={() => setViewOption(option)}
              sx={{
                bgcolor: viewOption === option ? 'primary.main' : 'transparent',
                color: viewOption === option ? 'white' : 'text.primary',
                borderRadius: 0,
                '&:hover': {
                  bgcolor: viewOption === option ? 'primary.dark' : 'rgba(0, 0, 0, 0.04)',
                },
              }}
            >
              {option}
            </Button>
          ))}
        </Box>

        {/* Date Pickers */}
        {viewOption === 'Day' && <DayPicker onChange={handleDateChange} />}
        {viewOption === 'Week' && <WeeklyPicker onChange={handleDateChange} />}
        {viewOption === 'Month' && <MonthPicker onChange={handleDateChange} />}
      </Box>

      {/* Conditionally render view */}
      {viewOption === 'Day' && <DayWorkReport dateRange={dateRange} />}
      {viewOption === 'Week' && <WeekWorkReport dateRange={dateRange} />}
      {viewOption === 'Month' && <MonthlyWorkReport dateRange={dateRange} />}
    </>
  );
}

export default Overview;
