import React, { useState, useEffect } from 'react';
import { Box, Button } from '@mui/material';
import MonthlyWorkReport from './component/MonthlyWorkReport';
import DayWorkReport from './component/DayWorkReport';
import WeekWorkReport from './component/WeekWorkReport';
import DayPicker from './TimePickers/DayPicker';
import WeeklyPicker from './TimePickers/WeeklyPicker';
import MonthPicker from './TimePickers/MonthPicker';
import { useDispatch } from 'react-redux';
import { ActivityActions } from '../../../slices/actions';
import dayjs from 'dayjs';

function Overview() {
  const dispatch = useDispatch();
  const [viewOption, setViewOption] = useState('Day');

  // Set default date range based on view option, preserving current context
  const getDefaultDateRange = (view, currentDate = dayjs()) => {
    switch(view) {
      case 'Day':
        return {
          startDate: currentDate.format('YYYY-MM-DD'),
          endDate: currentDate.format('YYYY-MM-DD')
        };
      case 'Week':
        return {
          startDate: currentDate.startOf('week').format('YYYY-MM-DD'),
          endDate: currentDate.endOf('week').format('YYYY-MM-DD')
        };
      case 'Month':
        return {
          startDate: currentDate.startOf('month').format('YYYY-MM-DD'),
          endDate: currentDate.endOf('month').format('YYYY-MM-DD')
        };
      default:
        return {
          startDate: currentDate.format('YYYY-MM-DD'),
          endDate: currentDate.format('YYYY-MM-DD')
        };
    }
  };

  const [dateRange, setDateRange] = useState(getDefaultDateRange('Day'));

  // Handle date changes from pickers
  const handleDateChange = (newDateRange) => {
    // All pickers now use the same format: {startDate, endDate}
    setDateRange(newDateRange);
  };

  // Update date range when view option changes, preserving current date context
  useEffect(() => {
    // Get current date from existing range or use today
    const currentDate = dateRange.startDate ? dayjs(dateRange.startDate) : dayjs();
    const newDateRange = getDefaultDateRange(viewOption, currentDate);
    setDateRange(newDateRange);
  }, [viewOption]);

  // Fetch activity data when date range or view changes
  useEffect(() => {
    if (dateRange.startDate && dateRange.endDate) {
      dispatch(ActivityActions.getUserActivity({
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
        view: viewOption.toLowerCase()
      }));
    }
  }, [dateRange, viewOption, dispatch]);

  return (
    <>
      <h1>Overview</h1>

      {/* View options and date picker */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 3,
          position: 'relative',
        }}
      >
        {/* Day/Week/Month tabs */}
        <Box
          sx={{
            display: 'flex',
            borderRadius: '4px',
            overflow: 'hidden',
            border: '1px solid #e0e0e0',
          }}
        >
          {['Day', 'Week', 'Month'].map((option) => (
            <Button
              key={option}
              onClick={() => setViewOption(option)}
              sx={{
                bgcolor: viewOption === option ? 'primary.main' : 'transparent',
                color: viewOption === option ? 'white' : 'text.primary',
                borderRadius: 0,
                '&:hover': {
                  bgcolor: viewOption === option ? 'primary.dark' : 'rgba(0, 0, 0, 0.04)',
                },
              }}
            >
              {option}
            </Button>
          ))}
        </Box>

        {/* Date Pickers */}
        {viewOption === 'Day' && <DayPicker onChange={handleDateChange} startDate={dateRange.startDate} endDate={dateRange.endDate} />}
        {viewOption === 'Week' && <WeeklyPicker onChange={handleDateChange} startDate={dateRange.startDate} />}
        {viewOption === 'Month' && <MonthPicker onChange={handleDateChange} selectedMonth={dateRange.startDate} />}
      </Box>

      {/* Conditionally render view */}
      {viewOption === 'Day' && <DayWorkReport dateRange={dateRange} />}
      {viewOption === 'Week' && <WeekWorkReport dateRange={dateRange} />}
      {viewOption === 'Month' && <MonthlyWorkReport dateRange={dateRange} />}
    </>
  );
}

export default Overview;
