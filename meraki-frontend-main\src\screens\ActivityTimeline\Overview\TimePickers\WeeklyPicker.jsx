import React, { useState, useRef, useEffect, useCallback } from "react";
import {
  IconButton,
  Typography,
  Box,
  Popper,
  Paper,
} from "@mui/material";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DateCalendar } from "@mui/x-date-pickers/DateCalendar";
import { PickersDay } from "@mui/x-date-pickers/PickersDay";
import dayjs from "dayjs";
import PropTypes from "prop-types";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import isoWeek from "dayjs/plugin/isoWeek";
import isBetweenPlugin from "dayjs/plugin/isBetween";
import { styled } from "@mui/material/styles";

// Extend dayjs with necessary plugins
dayjs.extend(isoWeek);
dayjs.extend(isBetweenPlugin);

// Custom styled component for selected week highlighting
const CustomPickersDay = styled(PickersDay, {
  shouldForwardProp: (prop) => prop !== "isSelected" && prop !== "isHovered",
})(({ theme, isSelected, isHovered, day }) => ({
  borderRadius: 0,
  ...(isSelected && {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.contrastText,
    "&:hover, &:focus": {
      backgroundColor: theme.palette.primary.main,
    },
  }),
  ...(isHovered && {
    backgroundColor: theme.palette.primary.light,
    "&:hover, &:focus": {
      backgroundColor: theme.palette.primary.light,
    },
  }),
  ...(day.day() === 0 && {
    borderTopLeftRadius: "50%",
    borderBottomLeftRadius: "50%",
  }),
  ...(day.day() === 6 && {
    borderTopRightRadius: "50%",
    borderBottomRightRadius: "50%",
  }),
}));

// Function to check if two days belong to the same week
const isInSameWeek = (dayA, dayB) => dayB && dayA.isSame(dayB, "week");

/**
 * Custom Day Component for Calendar
 * Highlights the entire week when a date is selected
 */
function Day(props) {
  const { day, selectedDay, hoveredDay, ...other } = props;

  return (
    <CustomPickersDay
      {...other}
      day={day}
      sx={{ px: 2.5 }}
      disableMargin
      selected={false}
      isSelected={isInSameWeek(day, selectedDay)}
      isHovered={isInSameWeek(day, hoveredDay)}
    />
  );
}

Day.propTypes = {
  day: PropTypes.object.isRequired,
  selectedDay: PropTypes.object,
  hoveredDay: PropTypes.object,
};

/**
 * WeeklyPicker Component
 * Allows users to pick a week using a date calendar
 */
const WeeklyPicker = ({ onChange, startDate }) => {
  // Initialize with provided date or current date
  const initialDate = startDate ? dayjs(startDate) : dayjs();

  // State to track the selected date
  const [selectedDate, setSelectedDate] = useState(initialDate);

  // Sync with external startDate prop changes
  useEffect(() => {
    if (startDate) {
      const newDate = dayjs(startDate);
      if (!newDate.isSame(selectedDate, 'week')) {
        setSelectedDate(newDate.startOf('week'));
      }
    }
  }, [startDate]);

  // State to manage the visibility of the date picker
  const [openPicker, setOpenPicker] = useState(false);

  // State to track hovered day for week highlighting
  const [hoveredDay, setHoveredDay] = useState(null);

  // Refs for detecting outside clicks
  const calendarRef = useRef(null);
  const calendarContainerRef = useRef(null);

  // Compute start and end of the selected week
  const startOfWeek = selectedDate.startOf("week");
  const endOfWeek = selectedDate.endOf("week");

  // Call onChange callback whenever selectedDate changes
  useEffect(() => {
    const formattedStart = startOfWeek.format("YYYY-MM-DD");
    const formattedEnd = endOfWeek.format("YYYY-MM-DD");

    // Pass the date range in the expected format
    onChange?.({
      startDate: formattedStart,
      endDate: formattedEnd
    });
  }, [selectedDate, onChange, startOfWeek, endOfWeek]);
  

  // Handle week selection when a new date is picked
  const handleDateChange = (date) => {
    if (date) {
      setSelectedDate(dayjs(date).startOf("week"));
      setOpenPicker(false);
    }
  };

  // Close the date picker when clicking outside
  const handleClickOutside = useCallback((event) => {
    if (
      calendarContainerRef.current &&
      !calendarContainerRef.current.contains(event.target) &&
      calendarRef.current &&
      !calendarRef.current.contains(event.target)
    ) {
      setOpenPicker(false);
    }
  }, []);
  
  useEffect(() => {
    if (openPicker) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [openPicker, handleClickOutside]);
  

  // Handlers to navigate weeks
  const goToPreviousWeek = () => setSelectedDate((prev) => prev.subtract(7, "day"));
  const goToNextWeek = () => setSelectedDate((prev) => prev.add(7, "day"));

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box display="flex" justifyContent="flex-end" alignItems="center" gap={1} position="relative">
        {/* Previous Week Button */}
        <IconButton onClick={goToPreviousWeek}>
          <ChevronLeftIcon sx={{ color: "grey.500" }} />
        </IconButton>

        {/* Display Selected Week Range */}
        <Typography
          variant="body1"
          sx={{
            color: "text.secondary",
            borderRadius: "4px",
            padding: "4px 8px",
          }}
        >
          {startOfWeek.format("ddd, MMM D")} - {endOfWeek.format("ddd, MMM D")}
        </Typography>

        {/* Calendar Icon Button */}
        <IconButton ref={calendarRef} onClick={() => setOpenPicker((prev) => !prev)}>
          <CalendarTodayIcon sx={{ color: "grey.500" }} />
        </IconButton>

        {/* Popper to display the Calendar */}
        <Popper
          ref={calendarContainerRef}
          open={openPicker}
          anchorEl={calendarRef.current}
          placement="bottom"
          disablePortal
          modifiers={[
            {
              name: "preventOverflow",
              options: { boundary: "window" },
            },
          ]}
        >
          <Paper elevation={3} sx={{ p: 1, position: "relative", right: "100px" }}>
            <DateCalendar
              value={selectedDate}
              onChange={handleDateChange}
              showDaysOutsideCurrentMonth
              displayWeekNumber
              slots={{ day: Day }}
              slotProps={{
                day: (ownerState) => ({
                  selectedDay: selectedDate,
                  hoveredDay,
                  onPointerEnter: () => setHoveredDay(ownerState.day),
                  onPointerLeave: () => setHoveredDay(null),
                }),
              }}
            />
          </Paper>
        </Popper>

        {/* Next Week Button */}
        <IconButton onClick={goToNextWeek}>
          <ChevronRightIcon sx={{ color: "grey.500" }} />
        </IconButton>
      </Box>
    </LocalizationProvider>
  );
};

WeeklyPicker.propTypes = {
  onChange: PropTypes.func,
  startDate: PropTypes.string,
};

export default WeeklyPicker;
