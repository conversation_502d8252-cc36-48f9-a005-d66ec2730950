{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\ActivityTimeline\\\\Overview\\\\component\\\\WeekWorkReport.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport PropTypes from \"prop-types\";\nimport { Avatar, Box, Card, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography, LinearProgress } from \"@mui/material\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { format, parseISO, addDays, startOfWeek, endOfWeek } from \"date-fns\";\nimport { ActivityActions } from \"../../../../slices/actions\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst getInitials = name => name.split(\" \").map(n => n[0]).join(\"\");\nconst getColor = value => {\n  if (value === \"Holiday\") {\n    return \"red\";\n  }\n  if (value === \"--\") {\n    return \"gray\";\n  }\n  const hours = parseInt(value.slice(0, 2), 10);\n  return hours >= 8 ? \"green\" : \"red\";\n};\nconst parseTimeToMinutes = timeStr => {\n  if (!timeStr || timeStr === \"--\" || timeStr === \"Holiday\") {\n    return 0;\n  }\n\n  // Handle formats like \"8h 30m\", \"45m\", \"8h\"\n  const hourMatch = timeStr.match(/(\\d+)h/);\n  const minuteMatch = timeStr.match(/(\\d+)m/);\n  const hours = hourMatch ? parseInt(hourMatch[1], 10) : 0;\n  const minutes = minuteMatch ? parseInt(minuteMatch[1], 10) : 0;\n  return hours * 60 + minutes;\n};\nconst getProgressValue = timeStr => {\n  const minutes = parseTimeToMinutes(timeStr);\n  const expectedMinutes = 8 * 60; // 8 hours = 480 minutes\n  return Math.min(minutes / expectedMinutes * 100, 100);\n};\nconst WeekWorkReport = ({\n  dateRange\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    multiUserActivityArr\n  } = useSelector(state => state.activity || {\n    multiUserActivityArr: []\n  });\n\n  // Use multiUserActivityArr for the ActivityTimeline components\n  const activityArr = multiUserActivityArr;\n  const [weekDays, setWeekDays] = useState([]);\n\n  // Format the selected week range for display\n  const startDate = dateRange !== null && dateRange !== void 0 && dateRange.startDate ? parseISO(dateRange.startDate) : startOfWeek(new Date());\n  const endDate = dateRange !== null && dateRange !== void 0 && dateRange.endDate ? parseISO(dateRange.endDate) : endOfWeek(new Date());\n  const displayWeekRange = `${format(startDate, \"EEE, MMM d, yyyy\")} – ${format(endDate, \"EEE, MMM d, yyyy\")}`;\n\n  // Generate week days array based on date range\n  useEffect(() => {\n    if (dateRange !== null && dateRange !== void 0 && dateRange.startDate && dateRange !== null && dateRange !== void 0 && dateRange.endDate) {\n      const start = parseISO(dateRange.startDate);\n      const days = [];\n\n      // Generate array of days in the week\n      for (let i = 0; i < 7; i++) {\n        const day = addDays(start, i);\n        days.push(format(day, \"MMM dd\"));\n      }\n      setWeekDays(days);\n\n      // Fetch activity data for the week\n      dispatch(ActivityActions.getUserActivity({\n        startDate: dateRange.startDate,\n        endDate: dateRange.endDate,\n        view: 'week'\n      }));\n    }\n  }, [dateRange, dispatch]);\n\n  // If data is not available, show placeholder\n  if (!activityArr || activityArr.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      p: 3,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: displayWeekRange\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        children: \"No employee data available\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this);\n  }\n  const progress = getProgressValue(time);\n  const progressColor = progress >= 100 ? '#4caf50' : progress >= 75 ? '#ff9800' : '#f44336';\n  return /*#__PURE__*/_jsxDEV(Box, {\n    p: 3,\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      mb: 2,\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: displayWeekRange\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Card,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this), weekDays.map((day, i) => /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              children: day\n            }, i, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Total\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: activityArr.map((emp, i) => /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 1,\n                children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                  children: getInitials(emp.name || \"\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 124,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: emp.name || \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 17\n            }, this), (emp.weekData || Array(7).fill(\"--\")).map((time, idx) => /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              sx: {\n                minWidth: 120\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: getColor(time),\n                    fontWeight: \"bold\",\n                    mb: 0.5\n                  },\n                  children: time\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 23\n                }, this), time !== \"--\" && time !== \"Holiday\" && /*#__PURE__*/_jsxDEV(LinearProgress, {\n                  variant: \"determinate\",\n                  value: progress,\n                  sx: {\n                    height: 6,\n                    borderRadius: 3,\n                    backgroundColor: 'rgba(0, 0, 0, 0.1)',\n                    '& .MuiLinearProgress-bar': {\n                      backgroundColor: progressColor,\n                      borderRadius: 3\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 21\n              }, this)\n            }, idx, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 19\n            }, this)), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: \"bold\"\n              },\n              children: emp.total || \"0h 00m\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this)]\n          }, i, true, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"caption\",\n      color: \"gray\",\n      mt: 2,\n      display: \"block\",\n      children: [\"\\u2139\\uFE0F Calculation based on \", /*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Time at Work\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 33\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 102,\n    columnNumber: 5\n  }, this);\n};\n_s(WeekWorkReport, \"qf94FIE0fG3aBu7iLej7sxDa2Bc=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = WeekWorkReport;\nWeekWorkReport.propTypes = {\n  dateRange: PropTypes.shape({\n    startDate: PropTypes.string,\n    endDate: PropTypes.string\n  })\n};\nexport default WeekWorkReport;\nvar _c;\n$RefreshReg$(_c, \"WeekWorkReport\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "PropTypes", "Avatar", "Box", "Card", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Typography", "LinearProgress", "useSelector", "useDispatch", "format", "parseISO", "addDays", "startOfWeek", "endOfWeek", "ActivityActions", "jsxDEV", "_jsxDEV", "getInitials", "name", "split", "map", "n", "join", "getColor", "value", "hours", "parseInt", "slice", "parseTimeToMinutes", "timeStr", "hourMatch", "match", "minuteMatch", "minutes", "getProgressValue", "expectedMinutes", "Math", "min", "WeekWorkReport", "date<PERSON><PERSON><PERSON>", "_s", "dispatch", "multiUserActivityArr", "state", "activity", "activityArr", "weekDays", "setWeekDays", "startDate", "Date", "endDate", "displayWeekRange", "start", "days", "i", "day", "push", "getUserActivity", "view", "length", "p", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "progress", "time", "progressColor", "display", "justifyContent", "alignItems", "mb", "component", "align", "emp", "gap", "weekData", "Array", "fill", "idx", "sx", "min<PERSON><PERSON><PERSON>", "color", "fontWeight", "height", "borderRadius", "backgroundColor", "total", "mt", "_c", "propTypes", "shape", "string", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/ActivityTimeline/Overview/component/WeekWorkReport.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport PropTypes from \"prop-types\";\nimport {\n  Avatar,\n  Box,\n  Card,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Typography,\n  LinearProgress\n} from \"@mui/material\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { format, parseISO, addDays, startOfWeek, endOfWeek } from \"date-fns\";\nimport { ActivityActions } from \"../../../../slices/actions\";\n\nconst getInitials = (name) =>\n  name.split(\" \").map((n) => n[0]).join(\"\");\n\nconst getColor = (value) => {\n  if (value === \"Holiday\") { return \"red\" }\n  if (value === \"--\") { return \"gray\" }\n  const hours = parseInt(value.slice(0, 2), 10);\n  return hours >= 8 ? \"green\" : \"red\";\n};\n\nconst parseTimeToMinutes = (timeStr) => {\n  if (!timeStr || timeStr === \"--\" || timeStr === \"Holiday\") { return 0 }\n\n  // Handle formats like \"8h 30m\", \"45m\", \"8h\"\n  const hourMatch = timeStr.match(/(\\d+)h/);\n  const minuteMatch = timeStr.match(/(\\d+)m/);\n\n  const hours = hourMatch ? parseInt(hourMatch[1], 10) : 0;\n  const minutes = minuteMatch ? parseInt(minuteMatch[1], 10) : 0;\n\n  return hours * 60 + minutes;\n};\n\nconst getProgressValue = (timeStr) => {\n  const minutes = parseTimeToMinutes(timeStr);\n  const expectedMinutes = 8 * 60; // 8 hours = 480 minutes\n  return Math.min((minutes / expectedMinutes) * 100, 100);\n};\n\nconst WeekWorkReport = ({ dateRange }) => {\n  const dispatch = useDispatch();\n  const { multiUserActivityArr } = useSelector((state) => state.activity || { multiUserActivityArr: [] });\n\n  // Use multiUserActivityArr for the ActivityTimeline components\n  const activityArr = multiUserActivityArr;\n  const [weekDays, setWeekDays] = useState([]);\n  \n  // Format the selected week range for display\n  const startDate = dateRange?.startDate ? parseISO(dateRange.startDate) : startOfWeek(new Date());\n  const endDate = dateRange?.endDate ? parseISO(dateRange.endDate) : endOfWeek(new Date());\n  \n  const displayWeekRange = `${format(startDate, \"EEE, MMM d, yyyy\")} – ${format(endDate, \"EEE, MMM d, yyyy\")}`;\n\n  // Generate week days array based on date range\n  useEffect(() => {\n    if (dateRange?.startDate && dateRange?.endDate) {\n      const start = parseISO(dateRange.startDate);\n      const days = [];\n      \n      // Generate array of days in the week\n      for (let i = 0; i < 7; i++) {\n        const day = addDays(start, i);\n        days.push(format(day, \"MMM dd\"));\n      }\n      \n      setWeekDays(days);\n      \n      // Fetch activity data for the week\n      dispatch(ActivityActions.getUserActivity({\n        startDate: dateRange.startDate,\n        endDate: dateRange.endDate,\n        view: 'week'\n      }));\n    }\n  }, [dateRange, dispatch]);\n\n  // If data is not available, show placeholder\n  if (!activityArr || activityArr.length === 0) {\n    return (\n      <Box p={3}>\n        <Typography variant=\"h6\" gutterBottom>\n          {displayWeekRange}\n        </Typography>\n        <Typography>No employee data available</Typography>\n      </Box>\n    );\n  }\n\n  const progress = getProgressValue(time);\nconst progressColor = progress >= 100 ? '#4caf50' : progress >= 75 ? '#ff9800': '#f44336';\n\n  return (\n    <Box p={3}>\n      <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\n        <Typography variant=\"h6\">\n          {displayWeekRange}\n        </Typography>\n      </Box>\n      <TableContainer component={Card}>\n        <Table>\n          <TableHead>\n            <TableRow>\n              <TableCell><strong>Name</strong></TableCell>\n              {weekDays.map((day, i) => (\n                <TableCell key={i} align=\"center\">{day}</TableCell>\n              ))}\n              <TableCell><strong>Total</strong></TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {activityArr.map((emp, i) => (\n              <TableRow key={i}>\n                <TableCell>\n                  <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                    <Avatar>{getInitials(emp.name || \"\")}</Avatar>\n                    <Typography>{emp.name || \"\"}</Typography>\n                  </Box>\n                </TableCell>\n                {(emp.weekData || Array(7).fill(\"--\")).map((time, idx) => (\n                  <TableCell key={idx} align=\"center\" sx={{ minWidth: 120 }}>\n                    <Box>\n                      <Typography\n                        variant=\"body2\"\n                        sx={{ color: getColor(time), fontWeight: \"bold\", mb: 0.5 }}\n                      >\n                        {time}\n                      </Typography>\n                      {time !== \"--\" && time !== \"Holiday\" && (\n                        <LinearProgress\n  variant=\"determinate\"\n  value={progress}\n  sx={{\n    height: 6,\n    borderRadius: 3,\n    backgroundColor: 'rgba(0, 0, 0, 0.1)',\n    '& .MuiLinearProgress-bar': { backgroundColor: progressColor, borderRadius: 3 }\n  }}\n/>\n                      )}\n                    </Box>\n                  </TableCell>\n                ))}\n                <TableCell sx={{ fontWeight: \"bold\" }}>{emp.total || \"0h 00m\"}</TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n      </TableContainer>\n      <Typography variant=\"caption\" color=\"gray\" mt={2} display=\"block\">\n        ℹ️ Calculation based on <strong>Time at Work</strong>\n      </Typography>\n    </Box>\n  );\n};\n\nWeekWorkReport.propTypes = {\n  dateRange: PropTypes.shape({\n    startDate: PropTypes.string,\n    endDate: PropTypes.string\n  })\n};\n\nexport default WeekWorkReport;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,SAAS,MAAM,YAAY;AAClC,SACEC,MAAM,EACNC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVC,cAAc,QACT,eAAe;AACtB,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,WAAW,EAAEC,SAAS,QAAQ,UAAU;AAC5E,SAASC,eAAe,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,WAAW,GAAIC,IAAI,IACvBA,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;AAE3C,MAAMC,QAAQ,GAAIC,KAAK,IAAK;EAC1B,IAAIA,KAAK,KAAK,SAAS,EAAE;IAAE,OAAO,KAAK;EAAC;EACxC,IAAIA,KAAK,KAAK,IAAI,EAAE;IAAE,OAAO,MAAM;EAAC;EACpC,MAAMC,KAAK,GAAGC,QAAQ,CAACF,KAAK,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EAC7C,OAAOF,KAAK,IAAI,CAAC,GAAG,OAAO,GAAG,KAAK;AACrC,CAAC;AAED,MAAMG,kBAAkB,GAAIC,OAAO,IAAK;EACtC,IAAI,CAACA,OAAO,IAAIA,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,SAAS,EAAE;IAAE,OAAO,CAAC;EAAC;;EAEtE;EACA,MAAMC,SAAS,GAAGD,OAAO,CAACE,KAAK,CAAC,QAAQ,CAAC;EACzC,MAAMC,WAAW,GAAGH,OAAO,CAACE,KAAK,CAAC,QAAQ,CAAC;EAE3C,MAAMN,KAAK,GAAGK,SAAS,GAAGJ,QAAQ,CAACI,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;EACxD,MAAMG,OAAO,GAAGD,WAAW,GAAGN,QAAQ,CAACM,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;EAE9D,OAAOP,KAAK,GAAG,EAAE,GAAGQ,OAAO;AAC7B,CAAC;AAED,MAAMC,gBAAgB,GAAIL,OAAO,IAAK;EACpC,MAAMI,OAAO,GAAGL,kBAAkB,CAACC,OAAO,CAAC;EAC3C,MAAMM,eAAe,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;EAChC,OAAOC,IAAI,CAACC,GAAG,CAAEJ,OAAO,GAAGE,eAAe,GAAI,GAAG,EAAE,GAAG,CAAC;AACzD,CAAC;AAED,MAAMG,cAAc,GAAGA,CAAC;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EACxC,MAAMC,QAAQ,GAAGjC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEkC;EAAqB,CAAC,GAAGnC,WAAW,CAAEoC,KAAK,IAAKA,KAAK,CAACC,QAAQ,IAAI;IAAEF,oBAAoB,EAAE;EAAG,CAAC,CAAC;;EAEvG;EACA,MAAMG,WAAW,GAAGH,oBAAoB;EACxC,MAAM,CAACI,QAAQ,EAAEC,WAAW,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;;EAE5C;EACA,MAAMsD,SAAS,GAAGT,SAAS,aAATA,SAAS,eAATA,SAAS,CAAES,SAAS,GAAGtC,QAAQ,CAAC6B,SAAS,CAACS,SAAS,CAAC,GAAGpC,WAAW,CAAC,IAAIqC,IAAI,CAAC,CAAC,CAAC;EAChG,MAAMC,OAAO,GAAGX,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEW,OAAO,GAAGxC,QAAQ,CAAC6B,SAAS,CAACW,OAAO,CAAC,GAAGrC,SAAS,CAAC,IAAIoC,IAAI,CAAC,CAAC,CAAC;EAExF,MAAME,gBAAgB,GAAG,GAAG1C,MAAM,CAACuC,SAAS,EAAE,kBAAkB,CAAC,MAAMvC,MAAM,CAACyC,OAAO,EAAE,kBAAkB,CAAC,EAAE;;EAE5G;EACAzD,SAAS,CAAC,MAAM;IACd,IAAI8C,SAAS,aAATA,SAAS,eAATA,SAAS,CAAES,SAAS,IAAIT,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEW,OAAO,EAAE;MAC9C,MAAME,KAAK,GAAG1C,QAAQ,CAAC6B,SAAS,CAACS,SAAS,CAAC;MAC3C,MAAMK,IAAI,GAAG,EAAE;;MAEf;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC1B,MAAMC,GAAG,GAAG5C,OAAO,CAACyC,KAAK,EAAEE,CAAC,CAAC;QAC7BD,IAAI,CAACG,IAAI,CAAC/C,MAAM,CAAC8C,GAAG,EAAE,QAAQ,CAAC,CAAC;MAClC;MAEAR,WAAW,CAACM,IAAI,CAAC;;MAEjB;MACAZ,QAAQ,CAAC3B,eAAe,CAAC2C,eAAe,CAAC;QACvCT,SAAS,EAAET,SAAS,CAACS,SAAS;QAC9BE,OAAO,EAAEX,SAAS,CAACW,OAAO;QAC1BQ,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACnB,SAAS,EAAEE,QAAQ,CAAC,CAAC;;EAEzB;EACA,IAAI,CAACI,WAAW,IAAIA,WAAW,CAACc,MAAM,KAAK,CAAC,EAAE;IAC5C,oBACE3C,OAAA,CAACnB,GAAG;MAAC+D,CAAC,EAAE,CAAE;MAAAC,QAAA,gBACR7C,OAAA,CAACX,UAAU;QAACyD,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAClCV;MAAgB;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eACbnD,OAAA,CAACX,UAAU;QAAAwD,QAAA,EAAC;MAA0B;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC;EAEV;EAEA,MAAMC,QAAQ,GAAGlC,gBAAgB,CAACmC,IAAI,CAAC;EACzC,MAAMC,aAAa,GAAGF,QAAQ,IAAI,GAAG,GAAG,SAAS,GAAGA,QAAQ,IAAI,EAAE,GAAG,SAAS,GAAE,SAAS;EAEvF,oBACEpD,OAAA,CAACnB,GAAG;IAAC+D,CAAC,EAAE,CAAE;IAAAC,QAAA,gBACR7C,OAAA,CAACnB,GAAG;MAAC0E,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,eAAe;MAACC,UAAU,EAAC,QAAQ;MAACC,EAAE,EAAE,CAAE;MAAAb,QAAA,eAC3E7C,OAAA,CAACX,UAAU;QAACyD,OAAO,EAAC,IAAI;QAAAD,QAAA,EACrBV;MAAgB;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eACNnD,OAAA,CAACd,cAAc;MAACyE,SAAS,EAAE7E,IAAK;MAAA+D,QAAA,eAC9B7C,OAAA,CAACjB,KAAK;QAAA8D,QAAA,gBACJ7C,OAAA,CAACb,SAAS;UAAA0D,QAAA,eACR7C,OAAA,CAACZ,QAAQ;YAAAyD,QAAA,gBACP7C,OAAA,CAACf,SAAS;cAAA4D,QAAA,eAAC7C,OAAA;gBAAA6C,QAAA,EAAQ;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,EAC3CrB,QAAQ,CAAC1B,GAAG,CAAC,CAACmC,GAAG,EAAED,CAAC,kBACnBtC,OAAA,CAACf,SAAS;cAAS2E,KAAK,EAAC,QAAQ;cAAAf,QAAA,EAAEN;YAAG,GAAtBD,CAAC;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAiC,CACnD,CAAC,eACFnD,OAAA,CAACf,SAAS;cAAA4D,QAAA,eAAC7C,OAAA;gBAAA6C,QAAA,EAAQ;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZnD,OAAA,CAAChB,SAAS;UAAA6D,QAAA,EACPhB,WAAW,CAACzB,GAAG,CAAC,CAACyD,GAAG,EAAEvB,CAAC,kBACtBtC,OAAA,CAACZ,QAAQ;YAAAyD,QAAA,gBACP7C,OAAA,CAACf,SAAS;cAAA4D,QAAA,eACR7C,OAAA,CAACnB,GAAG;gBAAC0E,OAAO,EAAC,MAAM;gBAACE,UAAU,EAAC,QAAQ;gBAACK,GAAG,EAAE,CAAE;gBAAAjB,QAAA,gBAC7C7C,OAAA,CAACpB,MAAM;kBAAAiE,QAAA,EAAE5C,WAAW,CAAC4D,GAAG,CAAC3D,IAAI,IAAI,EAAE;gBAAC;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,eAC9CnD,OAAA,CAACX,UAAU;kBAAAwD,QAAA,EAAEgB,GAAG,CAAC3D,IAAI,IAAI;gBAAE;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,EACX,CAACU,GAAG,CAACE,QAAQ,IAAIC,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE7D,GAAG,CAAC,CAACiD,IAAI,EAAEa,GAAG,kBACnDlE,OAAA,CAACf,SAAS;cAAW2E,KAAK,EAAC,QAAQ;cAACO,EAAE,EAAE;gBAAEC,QAAQ,EAAE;cAAI,CAAE;cAAAvB,QAAA,eACxD7C,OAAA,CAACnB,GAAG;gBAAAgE,QAAA,gBACF7C,OAAA,CAACX,UAAU;kBACTyD,OAAO,EAAC,OAAO;kBACfqB,EAAE,EAAE;oBAAEE,KAAK,EAAE9D,QAAQ,CAAC8C,IAAI,CAAC;oBAAEiB,UAAU,EAAE,MAAM;oBAAEZ,EAAE,EAAE;kBAAI,CAAE;kBAAAb,QAAA,EAE1DQ;gBAAI;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,EACZE,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,SAAS,iBAClCrD,OAAA,CAACV,cAAc;kBACrCwD,OAAO,EAAC,aAAa;kBACrBtC,KAAK,EAAE4C,QAAS;kBAChBe,EAAE,EAAE;oBACFI,MAAM,EAAE,CAAC;oBACTC,YAAY,EAAE,CAAC;oBACfC,eAAe,EAAE,oBAAoB;oBACrC,0BAA0B,EAAE;sBAAEA,eAAe,EAAEnB,aAAa;sBAAEkB,YAAY,EAAE;oBAAE;kBAChF;gBAAE;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACsB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC,GApBQe,GAAG;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAqBR,CACZ,CAAC,eACFnD,OAAA,CAACf,SAAS;cAACkF,EAAE,EAAE;gBAAEG,UAAU,EAAE;cAAO,CAAE;cAAAzB,QAAA,EAAEgB,GAAG,CAACa,KAAK,IAAI;YAAQ;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA,GA/B7Db,CAAC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgCN,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eACjBnD,OAAA,CAACX,UAAU;MAACyD,OAAO,EAAC,SAAS;MAACuB,KAAK,EAAC,MAAM;MAACM,EAAE,EAAE,CAAE;MAACpB,OAAO,EAAC,OAAO;MAAAV,QAAA,GAAC,oCACxC,eAAA7C,OAAA;QAAA6C,QAAA,EAAQ;MAAY;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAAC3B,EAAA,CAlHIF,cAAc;EAAA,QACD9B,WAAW,EACKD,WAAW;AAAA;AAAAqF,EAAA,GAFxCtD,cAAc;AAoHpBA,cAAc,CAACuD,SAAS,GAAG;EACzBtD,SAAS,EAAE5C,SAAS,CAACmG,KAAK,CAAC;IACzB9C,SAAS,EAAErD,SAAS,CAACoG,MAAM;IAC3B7C,OAAO,EAAEvD,SAAS,CAACoG;EACrB,CAAC;AACH,CAAC;AAED,eAAezD,cAAc;AAAC,IAAAsD,EAAA;AAAAI,YAAA,CAAAJ,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}