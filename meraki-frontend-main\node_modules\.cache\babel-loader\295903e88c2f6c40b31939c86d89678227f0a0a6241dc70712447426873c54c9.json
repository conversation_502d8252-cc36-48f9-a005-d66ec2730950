{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\ActivityTimeline\\\\Overview\\\\Overview.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Button } from '@mui/material';\nimport MonthlyWorkReport from './component/MonthlyWorkReport';\nimport DayWorkReport from './component/DayWorkReport';\nimport WeekWorkReport from './component/WeekWorkReport';\nimport DayPicker from './TimePickers/DayPicker';\nimport WeeklyPicker from './TimePickers/WeeklyPicker';\nimport MonthPicker from './TimePickers/MonthPicker';\nimport { useDispatch } from 'react-redux';\nimport { ActivityActions } from '../../../slices/actions';\nimport dayjs from 'dayjs';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction Overview() {\n  _s();\n  const dispatch = useDispatch();\n  const [viewOption, setViewOption] = useState('Month');\n  const [dateRange, setDateRange] = useState({\n    startDate: dayjs().format('YYYY-MM-DD'),\n    endDate: dayjs().format('YYYY-MM-DD')\n  });\n\n  // Handle date changes from pickers\n  const handleDateChange = newDateRange => {\n    setDateRange(newDateRange);\n  };\n\n  // Fetch activity data when date range or view changes\n  useEffect(() => {\n    if (dateRange.startDate && dateRange.endDate) {\n      dispatch(ActivityActions.getUserActivity({\n        startDate: dateRange.startDate,\n        endDate: dateRange.endDate,\n        view: viewOption.toLowerCase()\n      }));\n    }\n  }, [dateRange, viewOption, dispatch]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Overview\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3,\n        position: 'relative'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          borderRadius: '4px',\n          overflow: 'hidden',\n          border: '1px solid #e0e0e0'\n        },\n        children: ['Day', 'Week', 'Month'].map(option => /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setViewOption(option),\n          sx: {\n            bgcolor: viewOption === option ? 'primary.main' : 'transparent',\n            color: viewOption === option ? 'white' : 'text.primary',\n            borderRadius: 0,\n            '&:hover': {\n              bgcolor: viewOption === option ? 'primary.dark' : 'rgba(0, 0, 0, 0.04)'\n            }\n          },\n          children: option\n        }, option, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), viewOption === 'Day' && /*#__PURE__*/_jsxDEV(DayPicker, {\n        onChange: handleDateChange\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 34\n      }, this), viewOption === 'Week' && /*#__PURE__*/_jsxDEV(WeeklyPicker, {\n        onChange: handleDateChange\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 35\n      }, this), viewOption === 'Month' && /*#__PURE__*/_jsxDEV(MonthPicker, {\n        onChange: handleDateChange\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 36\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this), viewOption === 'Day' && /*#__PURE__*/_jsxDEV(DayWorkReport, {\n      dateRange: dateRange\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 32\n    }, this), viewOption === 'Week' && /*#__PURE__*/_jsxDEV(WeekWorkReport, {\n      dateRange: dateRange\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 33\n    }, this), viewOption === 'Month' && /*#__PURE__*/_jsxDEV(MonthlyWorkReport, {\n      dateRange: dateRange\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 34\n    }, this)]\n  }, void 0, true);\n}\n_s(Overview, \"EHfCClpvlvSU9pTQoKeMN//baPQ=\", false, function () {\n  return [useDispatch];\n});\n_c = Overview;\nexport default Overview;\nvar _c;\n$RefreshReg$(_c, \"Overview\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "<PERSON><PERSON>", "MonthlyWorkReport", "DayWorkReport", "WeekWorkReport", "DayPicker", "WeeklyPicker", "MonthPicker", "useDispatch", "ActivityActions", "dayjs", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Overview", "_s", "dispatch", "viewOption", "setViewOption", "date<PERSON><PERSON><PERSON>", "setDateRange", "startDate", "format", "endDate", "handleDateChange", "newDateRange", "getUserActivity", "view", "toLowerCase", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "display", "justifyContent", "alignItems", "mb", "position", "borderRadius", "overflow", "border", "map", "option", "onClick", "bgcolor", "color", "onChange", "_c", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/ActivityTimeline/Overview/Overview.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { Box, Button } from '@mui/material';\r\nimport MonthlyWorkReport from './component/MonthlyWorkReport';\r\nimport DayWorkReport from './component/DayWorkReport';\r\nimport WeekWorkReport from './component/WeekWorkReport';\r\nimport DayPicker from './TimePickers/DayPicker';\r\nimport WeeklyPicker from './TimePickers/WeeklyPicker';\r\nimport MonthPicker from './TimePickers/MonthPicker';\r\nimport { useDispatch } from 'react-redux';\r\nimport { ActivityActions } from '../../../slices/actions';\r\nimport dayjs from 'dayjs';\r\n\r\nfunction Overview() {\r\n  const dispatch = useDispatch();\r\n  const [viewOption, setViewOption] = useState('Month');\r\n  const [dateRange, setDateRange] = useState({\r\n    startDate: dayjs().format('YYYY-MM-DD'),\r\n    endDate: dayjs().format('YYYY-MM-DD')\r\n  });\r\n\r\n  // Handle date changes from pickers\r\n  const handleDateChange = (newDateRange) => {\r\n    setDateRange(newDateRange);\r\n  };\r\n\r\n  // Fetch activity data when date range or view changes\r\n  useEffect(() => {\r\n    if (dateRange.startDate && dateRange.endDate) {\r\n      dispatch(ActivityActions.getUserActivity({\r\n        startDate: dateRange.startDate,\r\n        endDate: dateRange.endDate,\r\n        view: viewOption.toLowerCase()\r\n      }));\r\n    }\r\n  }, [dateRange, viewOption, dispatch]);\r\n\r\n  return (\r\n    <>\r\n      <h1>Overview</h1>\r\n\r\n      {/* View options and date picker */}\r\n      <Box\r\n        sx={{\r\n          display: 'flex',\r\n          justifyContent: 'space-between',\r\n          alignItems: 'center',\r\n          mb: 3,\r\n          position: 'relative',\r\n        }}\r\n      >\r\n        {/* Day/Week/Month tabs */}\r\n        <Box\r\n          sx={{\r\n            display: 'flex',\r\n            borderRadius: '4px',\r\n            overflow: 'hidden',\r\n            border: '1px solid #e0e0e0',\r\n          }}\r\n        >\r\n          {['Day', 'Week', 'Month'].map((option) => (\r\n            <Button\r\n              key={option}\r\n              onClick={() => setViewOption(option)}\r\n              sx={{\r\n                bgcolor: viewOption === option ? 'primary.main' : 'transparent',\r\n                color: viewOption === option ? 'white' : 'text.primary',\r\n                borderRadius: 0,\r\n                '&:hover': {\r\n                  bgcolor: viewOption === option ? 'primary.dark' : 'rgba(0, 0, 0, 0.04)',\r\n                },\r\n              }}\r\n            >\r\n              {option}\r\n            </Button>\r\n          ))}\r\n        </Box>\r\n\r\n        {/* Date Pickers */}\r\n        {viewOption === 'Day' && <DayPicker onChange={handleDateChange} />}\r\n        {viewOption === 'Week' && <WeeklyPicker onChange={handleDateChange} />}\r\n        {viewOption === 'Month' && <MonthPicker onChange={handleDateChange} />}\r\n      </Box>\r\n\r\n      {/* Conditionally render view */}\r\n      {viewOption === 'Day' && <DayWorkReport dateRange={dateRange} />}\r\n      {viewOption === 'Week' && <WeekWorkReport dateRange={dateRange} />}\r\n      {viewOption === 'Month' && <MonthlyWorkReport dateRange={dateRange} />}\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Overview;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,MAAM,QAAQ,eAAe;AAC3C,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,SAASC,QAAQA,CAAA,EAAG;EAAAC,EAAA;EAClB,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,OAAO,CAAC;EACrD,MAAM,CAACsB,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAC;IACzCwB,SAAS,EAAEZ,KAAK,CAAC,CAAC,CAACa,MAAM,CAAC,YAAY,CAAC;IACvCC,OAAO,EAAEd,KAAK,CAAC,CAAC,CAACa,MAAM,CAAC,YAAY;EACtC,CAAC,CAAC;;EAEF;EACA,MAAME,gBAAgB,GAAIC,YAAY,IAAK;IACzCL,YAAY,CAACK,YAAY,CAAC;EAC5B,CAAC;;EAED;EACA3B,SAAS,CAAC,MAAM;IACd,IAAIqB,SAAS,CAACE,SAAS,IAAIF,SAAS,CAACI,OAAO,EAAE;MAC5CP,QAAQ,CAACR,eAAe,CAACkB,eAAe,CAAC;QACvCL,SAAS,EAAEF,SAAS,CAACE,SAAS;QAC9BE,OAAO,EAAEJ,SAAS,CAACI,OAAO;QAC1BI,IAAI,EAAEV,UAAU,CAACW,WAAW,CAAC;MAC/B,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACT,SAAS,EAAEF,UAAU,EAAED,QAAQ,CAAC,CAAC;EAErC,oBACEL,OAAA,CAAAE,SAAA;IAAAgB,QAAA,gBACElB,OAAA;MAAAkB,QAAA,EAAI;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAGjBtB,OAAA,CAACZ,GAAG;MACFmC,EAAE,EAAE;QACFC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBC,EAAE,EAAE,CAAC;QACLC,QAAQ,EAAE;MACZ,CAAE;MAAAV,QAAA,gBAGFlB,OAAA,CAACZ,GAAG;QACFmC,EAAE,EAAE;UACFC,OAAO,EAAE,MAAM;UACfK,YAAY,EAAE,KAAK;UACnBC,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAE;QACV,CAAE;QAAAb,QAAA,EAED,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAACc,GAAG,CAAEC,MAAM,iBACnCjC,OAAA,CAACX,MAAM;UAEL6C,OAAO,EAAEA,CAAA,KAAM3B,aAAa,CAAC0B,MAAM,CAAE;UACrCV,EAAE,EAAE;YACFY,OAAO,EAAE7B,UAAU,KAAK2B,MAAM,GAAG,cAAc,GAAG,aAAa;YAC/DG,KAAK,EAAE9B,UAAU,KAAK2B,MAAM,GAAG,OAAO,GAAG,cAAc;YACvDJ,YAAY,EAAE,CAAC;YACf,SAAS,EAAE;cACTM,OAAO,EAAE7B,UAAU,KAAK2B,MAAM,GAAG,cAAc,GAAG;YACpD;UACF,CAAE;UAAAf,QAAA,EAEDe;QAAM,GAXFA,MAAM;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAYL,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAGLhB,UAAU,KAAK,KAAK,iBAAIN,OAAA,CAACP,SAAS;QAAC4C,QAAQ,EAAExB;MAAiB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACjEhB,UAAU,KAAK,MAAM,iBAAIN,OAAA,CAACN,YAAY;QAAC2C,QAAQ,EAAExB;MAAiB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACrEhB,UAAU,KAAK,OAAO,iBAAIN,OAAA,CAACL,WAAW;QAAC0C,QAAQ,EAAExB;MAAiB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnE,CAAC,EAGLhB,UAAU,KAAK,KAAK,iBAAIN,OAAA,CAACT,aAAa;MAACiB,SAAS,EAAEA;IAAU;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAC/DhB,UAAU,KAAK,MAAM,iBAAIN,OAAA,CAACR,cAAc;MAACgB,SAAS,EAAEA;IAAU;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACjEhB,UAAU,KAAK,OAAO,iBAAIN,OAAA,CAACV,iBAAiB;MAACkB,SAAS,EAAEA;IAAU;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eACtE,CAAC;AAEP;AAAClB,EAAA,CA7EQD,QAAQ;EAAA,QACEP,WAAW;AAAA;AAAA0C,EAAA,GADrBnC,QAAQ;AA+EjB,eAAeA,QAAQ;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}