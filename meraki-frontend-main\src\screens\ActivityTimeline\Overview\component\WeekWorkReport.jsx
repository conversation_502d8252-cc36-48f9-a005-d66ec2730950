import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import {
  Avatar,
  Box,
  Card,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  LinearProgress
} from "@mui/material";
import { useSelector, useDispatch } from "react-redux";
import { format, parseISO, addDays, startOfWeek, endOfWeek } from "date-fns";
import { ActivityActions } from "../../../../slices/actions";

const getInitials = (name) =>
  name.split(" ").map((n) => n[0]).join("");

const getColor = (value) => {
  if (value === "Holiday") { return "red" }
  if (value === "--") { return "gray" }
  const hours = parseInt(value.slice(0, 2), 10);
  return hours >= 8 ? "green" : "red";
};

const parseTimeToMinutes = (timeStr) => {
  if (!timeStr || timeStr === "--" || timeStr === "Holiday") {return 0 } 

  // Use named capture groups
  const hourMatch = timeStr.match(/(?<hours>\d+)h/);
  const minuteMatch = timeStr.match(/(?<minutes>\d+)m/);

  const hours = hourMatch?.groups?.hours ? parseInt(hourMatch.groups.hours, 10) : 0;
  const minutes = minuteMatch?.groups?.minutes ? parseInt(minuteMatch.groups.minutes, 10) : 0;

  return (hours * 60) + minutes; // clarify operator precedence
};

const getProgressValue = (timeStr) => {
  const minutes = parseTimeToMinutes(timeStr);
  const expectedMinutes = 8 * 60; // 8 hours = 480 minutes
  return Math.min((minutes / expectedMinutes) * 100, 100);
};

const WeekWorkReport = ({ dateRange }) => {
  const dispatch = useDispatch();
  const { multiUserActivityArr } = useSelector((state) => state.activity || { multiUserActivityArr: [] });

  // Use multiUserActivityArr for the ActivityTimeline components
  const activityArr = multiUserActivityArr;
  const [weekDays, setWeekDays] = useState([]);
  
  // Format the selected week range for display
  const startDate = dateRange?.startDate ? parseISO(dateRange.startDate) : startOfWeek(new Date());
  const endDate = dateRange?.endDate ? parseISO(dateRange.endDate) : endOfWeek(new Date());
  
  const displayWeekRange = `${format(startDate, "EEE, MMM d, yyyy")} – ${format(endDate, "EEE, MMM d, yyyy")}`;

  // Generate week days array based on date range
  useEffect(() => {
    if (dateRange?.startDate && dateRange?.endDate) {
      const start = parseISO(dateRange.startDate);
      const days = [];
      
      // Generate array of days in the week
      for (let i = 0; i < 7; i++) {
        const day = addDays(start, i);
        days.push(format(day, "MMM dd"));
      }
      
      setWeekDays(days);
      
      // Fetch activity data for the week
      dispatch(ActivityActions.getUserActivity({
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
        view: 'week'
      }));
    }
  }, [dateRange, dispatch]);

  // If data is not available, show placeholder
  if (!activityArr || activityArr.length === 0) {
    return (
      <Box p={3}>
        <Typography variant="h6" gutterBottom>
          {displayWeekRange}
        </Typography>
        <Typography>No employee data available</Typography>
      </Box>
    );
  }

  return (
    <Box p={3}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="h6">
          {displayWeekRange}
        </Typography>
      </Box>
      <TableContainer component={Card}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell><strong>Name</strong></TableCell>
              {weekDays.map((day, i) => (
                <TableCell key={i} align="center">{day}</TableCell>
              ))}
              <TableCell><strong>Total</strong></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {activityArr.map((emp, i) => (
              <TableRow key={i}>
                <TableCell>
                  <Box display="flex" alignItems="center" gap={1}>
                    <Avatar>{getInitials(emp.name || "")}</Avatar>
                    <Typography>{emp.name || ""}</Typography>
                  </Box>
                </TableCell>
                {(emp.weekData || Array(7).fill("--")).map((time, idx) => (
                  <TableCell key={idx} align="center" sx={{ minWidth: 120 }}>
                    <Box>
                      <Typography
                        variant="body2"
                        sx={{ color: getColor(time), fontWeight: "bold", mb: 0.5 }}
                      >
                        {time}
                      </Typography>
                      {time !== "--" && time !== "Holiday" && (
                        <LinearProgress
                          variant="determinate"
                          value={getProgressValue(time)}
                          sx={{
                            height: 6,
                            borderRadius: 3,
                            backgroundColor: 'rgba(0, 0, 0, 0.1)',
                            '& .MuiLinearProgress-bar': { backgroundColor: getProgressValue(time) >= 100 ? '#4caf50' : getProgressValue(time) >= 75 ? '#ff9800' : '#f44336',borderRadius: 3,
                            },
                          }}
                        />
                      )}
                    </Box>
                  </TableCell>
                ))}
                <TableCell sx={{ fontWeight: "bold" }}>{emp.total || "0h 00m"}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      <Typography variant="caption" color="gray" mt={2} display="block">
        ℹ️ Calculation based on <strong>Time at Work</strong>
      </Typography>
    </Box>
  );
};

WeekWorkReport.propTypes = {
  dateRange: PropTypes.shape({
    startDate: PropTypes.string,
    endDate: PropTypes.string
  })
};

export default WeekWorkReport;