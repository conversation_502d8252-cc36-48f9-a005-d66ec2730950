import { createSlice } from "@reduxjs/toolkit";


export const ActivitySlice = createSlice({
    name: "Activity",
    initialState: {
        activityArr: [], // For single user activity (legacy)
        multiUserActivityArr: [], // For multi-user activity data (new)
    },
    reducers: {
        createTodayGoal: () => {
        },
        createTodayStatus: () => {
        },
        getUserActivitySuccessfull: (state,action) => {
            console.log("REDUCER ACTIVITY LOG - Received payload:", action.payload);

            // Check if this is multi-user data (has name field) or single user data
            if (Array.isArray(action.payload) && action.payload.length > 0 && action.payload[0].name) {
                // This is multi-user activity data
                state.multiUserActivityArr = action.payload;
                console.log("REDUCER ACTIVITY LOG - Set multi-user array:", action.payload.length, "items");
            } else if (Array.isArray(action.payload)) {
                // This is single user activity data (legacy)
                state.activityArr = action.payload;
                console.log("REDUCER ACTIVITY LOG - Set single user array:", action.payload.length, "items");
            } else if (action.payload && typeof action.payload === 'object') {
                // Single object response (for individual user history)
                state.activityArr = [action.payload];
                console.log("REDUCER ACTIVITY LOG - Set single object as array");
            } else {
                state.activityArr = [];
                console.log("REDUCER ACTIVITY LOG - Set empty array");
            }
        },
        getUserActivity: () => {
        },
        checkOutStatusUpdate: () => {},
        breakStartRed: () => {},
        breakEndRed: () => {},
        lateCheckIn: () => {},
        earlyCheckOut: () => {},
        idelStartRed: () => {},
        idelEndRed: () => {},
        productivityStatusRed: () => {},
        overLimitBreakRed: () => {},
        eraseActivity: (state = []) => {
            state.activityArr = []
        },
        createTimelineRequest: () => {},
        updateTimelineRequest: () => {},
        getTimelineRequests: () => {}
    }
});

export default ActivitySlice;