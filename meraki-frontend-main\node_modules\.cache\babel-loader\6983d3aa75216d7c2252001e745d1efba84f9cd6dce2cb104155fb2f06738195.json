{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\ActivityTimeline\\\\Overview\\\\component\\\\WeekWorkReport.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport PropTypes from \"prop-types\";\nimport { Avatar, Box, Card, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography, LinearProgress } from \"@mui/material\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { format, parseISO, addDays, startOfWeek, endOfWeek } from \"date-fns\";\nimport { ActivityActions } from \"../../../../slices/actions\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst getInitials = name => name.split(\" \").map(n => n[0]).join(\"\");\nconst getColor = value => {\n  if (value === \"Holiday\") {\n    return \"red\";\n  }\n  if (value === \"--\") {\n    return \"gray\";\n  }\n  const hours = parseInt(value.slice(0, 2), 10);\n  return hours >= 8 ? \"green\" : \"red\";\n};\nconst parseTimeToMinutes = timeStr => {\n  var _hourMatch$groups, _minuteMatch$groups;\n  if (!timeStr || timeStr === \"--\" || timeStr === \"Holiday\") return 0;\n\n  // Use named capture groups\n  const hourMatch = timeStr.match(/(?<hours>\\d+)h/);\n  const minuteMatch = timeStr.match(/(?<minutes>\\d+)m/);\n  const hours = hourMatch !== null && hourMatch !== void 0 && (_hourMatch$groups = hourMatch.groups) !== null && _hourMatch$groups !== void 0 && _hourMatch$groups.hours ? parseInt(hourMatch.groups.hours, 10) : 0;\n  const minutes = minuteMatch !== null && minuteMatch !== void 0 && (_minuteMatch$groups = minuteMatch.groups) !== null && _minuteMatch$groups !== void 0 && _minuteMatch$groups.minutes ? parseInt(minuteMatch.groups.minutes, 10) : 0;\n  return hours * 60 + minutes; // clarify operator precedence\n};\nconst getProgressValue = timeStr => {\n  const minutes = parseTimeToMinutes(timeStr);\n  const expectedMinutes = 8 * 60; // 8 hours = 480 minutes\n  return Math.min(minutes / expectedMinutes * 100, 100);\n};\nconst WeekWorkReport = ({\n  dateRange\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    multiUserActivityArr\n  } = useSelector(state => state.activity || {\n    multiUserActivityArr: []\n  });\n\n  // Use multiUserActivityArr for the ActivityTimeline components\n  const activityArr = multiUserActivityArr;\n  const [weekDays, setWeekDays] = useState([]);\n\n  // Format the selected week range for display\n  const startDate = dateRange !== null && dateRange !== void 0 && dateRange.startDate ? parseISO(dateRange.startDate) : startOfWeek(new Date());\n  const endDate = dateRange !== null && dateRange !== void 0 && dateRange.endDate ? parseISO(dateRange.endDate) : endOfWeek(new Date());\n  const displayWeekRange = `${format(startDate, \"EEE, MMM d, yyyy\")} – ${format(endDate, \"EEE, MMM d, yyyy\")}`;\n\n  // Generate week days array based on date range\n  useEffect(() => {\n    if (dateRange !== null && dateRange !== void 0 && dateRange.startDate && dateRange !== null && dateRange !== void 0 && dateRange.endDate) {\n      const start = parseISO(dateRange.startDate);\n      const days = [];\n\n      // Generate array of days in the week\n      for (let i = 0; i < 7; i++) {\n        const day = addDays(start, i);\n        days.push(format(day, \"MMM dd\"));\n      }\n      setWeekDays(days);\n\n      // Fetch activity data for the week\n      dispatch(ActivityActions.getUserActivity({\n        startDate: dateRange.startDate,\n        endDate: dateRange.endDate,\n        view: 'week'\n      }));\n    }\n  }, [dateRange, dispatch]);\n\n  // If data is not available, show placeholder\n  if (!activityArr || activityArr.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      p: 3,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: displayWeekRange\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        children: \"No employee data available\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    p: 3,\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      mb: 2,\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: displayWeekRange\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Card,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this), weekDays.map((day, i) => /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              children: day\n            }, i, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Total\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: activityArr.map((emp, i) => /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 1,\n                children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                  children: getInitials(emp.name || \"\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 121,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: emp.name || \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 17\n            }, this), (emp.weekData || Array(7).fill(\"--\")).map((time, idx) => /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              sx: {\n                minWidth: 120\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: getColor(time),\n                    fontWeight: \"bold\",\n                    mb: 0.5\n                  },\n                  children: time\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 128,\n                  columnNumber: 23\n                }, this), time !== \"--\" && time !== \"Holiday\" && /*#__PURE__*/_jsxDEV(LinearProgress, {\n                  variant: \"determinate\",\n                  value: getProgressValue(time),\n                  sx: {\n                    height: 6,\n                    borderRadius: 3,\n                    backgroundColor: 'rgba(0, 0, 0, 0.1)',\n                    '& .MuiLinearProgress-bar': {\n                      backgroundColor: getProgressValue(time) >= 100 ? '#4caf50' : getProgressValue(time) >= 75 ? '#ff9800' : '#f44336',\n                      borderRadius: 3\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 21\n              }, this)\n            }, idx, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 19\n            }, this)), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: \"bold\"\n              },\n              children: emp.total || \"0h 00m\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 17\n            }, this)]\n          }, i, true, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"caption\",\n      color: \"gray\",\n      mt: 2,\n      display: \"block\",\n      children: [\"\\u2139\\uFE0F Calculation based on \", /*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Time at Work\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 33\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 5\n  }, this);\n};\n_s(WeekWorkReport, \"qf94FIE0fG3aBu7iLej7sxDa2Bc=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = WeekWorkReport;\nWeekWorkReport.propTypes = {\n  dateRange: PropTypes.shape({\n    startDate: PropTypes.string,\n    endDate: PropTypes.string\n  })\n};\nexport default WeekWorkReport;\nvar _c;\n$RefreshReg$(_c, \"WeekWorkReport\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "PropTypes", "Avatar", "Box", "Card", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Typography", "LinearProgress", "useSelector", "useDispatch", "format", "parseISO", "addDays", "startOfWeek", "endOfWeek", "ActivityActions", "jsxDEV", "_jsxDEV", "getInitials", "name", "split", "map", "n", "join", "getColor", "value", "hours", "parseInt", "slice", "parseTimeToMinutes", "timeStr", "_hourMatch$groups", "_minuteMatch$groups", "hourMatch", "match", "minuteMatch", "groups", "minutes", "getProgressValue", "expectedMinutes", "Math", "min", "WeekWorkReport", "date<PERSON><PERSON><PERSON>", "_s", "dispatch", "multiUserActivityArr", "state", "activity", "activityArr", "weekDays", "setWeekDays", "startDate", "Date", "endDate", "displayWeekRange", "start", "days", "i", "day", "push", "getUserActivity", "view", "length", "p", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "justifyContent", "alignItems", "mb", "component", "align", "emp", "gap", "weekData", "Array", "fill", "time", "idx", "sx", "min<PERSON><PERSON><PERSON>", "color", "fontWeight", "height", "borderRadius", "backgroundColor", "total", "mt", "_c", "propTypes", "shape", "string", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/ActivityTimeline/Overview/component/WeekWorkReport.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport PropTypes from \"prop-types\";\nimport {\n  Avatar,\n  Box,\n  Card,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Typography,\n  LinearProgress\n} from \"@mui/material\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { format, parseISO, addDays, startOfWeek, endOfWeek } from \"date-fns\";\nimport { ActivityActions } from \"../../../../slices/actions\";\n\nconst getInitials = (name) =>\n  name.split(\" \").map((n) => n[0]).join(\"\");\n\nconst getColor = (value) => {\n  if (value === \"Holiday\") { return \"red\" }\n  if (value === \"--\") { return \"gray\" }\n  const hours = parseInt(value.slice(0, 2), 10);\n  return hours >= 8 ? \"green\" : \"red\";\n};\n\nconst parseTimeToMinutes = (timeStr) => {\n  if (!timeStr || timeStr === \"--\" || timeStr === \"Holiday\") return 0;\n\n  // Use named capture groups\n  const hourMatch = timeStr.match(/(?<hours>\\d+)h/);\n  const minuteMatch = timeStr.match(/(?<minutes>\\d+)m/);\n\n  const hours = hourMatch?.groups?.hours ? parseInt(hourMatch.groups.hours, 10) : 0;\n  const minutes = minuteMatch?.groups?.minutes ? parseInt(minuteMatch.groups.minutes, 10) : 0;\n\n  return (hours * 60) + minutes; // clarify operator precedence\n};\n\nconst getProgressValue = (timeStr) => {\n  const minutes = parseTimeToMinutes(timeStr);\n  const expectedMinutes = 8 * 60; // 8 hours = 480 minutes\n  return Math.min((minutes / expectedMinutes) * 100, 100);\n};\n\nconst WeekWorkReport = ({ dateRange }) => {\n  const dispatch = useDispatch();\n  const { multiUserActivityArr } = useSelector((state) => state.activity || { multiUserActivityArr: [] });\n\n  // Use multiUserActivityArr for the ActivityTimeline components\n  const activityArr = multiUserActivityArr;\n  const [weekDays, setWeekDays] = useState([]);\n  \n  // Format the selected week range for display\n  const startDate = dateRange?.startDate ? parseISO(dateRange.startDate) : startOfWeek(new Date());\n  const endDate = dateRange?.endDate ? parseISO(dateRange.endDate) : endOfWeek(new Date());\n  \n  const displayWeekRange = `${format(startDate, \"EEE, MMM d, yyyy\")} – ${format(endDate, \"EEE, MMM d, yyyy\")}`;\n\n  // Generate week days array based on date range\n  useEffect(() => {\n    if (dateRange?.startDate && dateRange?.endDate) {\n      const start = parseISO(dateRange.startDate);\n      const days = [];\n      \n      // Generate array of days in the week\n      for (let i = 0; i < 7; i++) {\n        const day = addDays(start, i);\n        days.push(format(day, \"MMM dd\"));\n      }\n      \n      setWeekDays(days);\n      \n      // Fetch activity data for the week\n      dispatch(ActivityActions.getUserActivity({\n        startDate: dateRange.startDate,\n        endDate: dateRange.endDate,\n        view: 'week'\n      }));\n    }\n  }, [dateRange, dispatch]);\n\n  // If data is not available, show placeholder\n  if (!activityArr || activityArr.length === 0) {\n    return (\n      <Box p={3}>\n        <Typography variant=\"h6\" gutterBottom>\n          {displayWeekRange}\n        </Typography>\n        <Typography>No employee data available</Typography>\n      </Box>\n    );\n  }\n\n  return (\n    <Box p={3}>\n      <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\n        <Typography variant=\"h6\">\n          {displayWeekRange}\n        </Typography>\n      </Box>\n      <TableContainer component={Card}>\n        <Table>\n          <TableHead>\n            <TableRow>\n              <TableCell><strong>Name</strong></TableCell>\n              {weekDays.map((day, i) => (\n                <TableCell key={i} align=\"center\">{day}</TableCell>\n              ))}\n              <TableCell><strong>Total</strong></TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {activityArr.map((emp, i) => (\n              <TableRow key={i}>\n                <TableCell>\n                  <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                    <Avatar>{getInitials(emp.name || \"\")}</Avatar>\n                    <Typography>{emp.name || \"\"}</Typography>\n                  </Box>\n                </TableCell>\n                {(emp.weekData || Array(7).fill(\"--\")).map((time, idx) => (\n                  <TableCell key={idx} align=\"center\" sx={{ minWidth: 120 }}>\n                    <Box>\n                      <Typography\n                        variant=\"body2\"\n                        sx={{ color: getColor(time), fontWeight: \"bold\", mb: 0.5 }}\n                      >\n                        {time}\n                      </Typography>\n                      {time !== \"--\" && time !== \"Holiday\" && (\n                        <LinearProgress\n                          variant=\"determinate\"\n                          value={getProgressValue(time)}\n                          sx={{\n                            height: 6,\n                            borderRadius: 3,\n                            backgroundColor: 'rgba(0, 0, 0, 0.1)',\n                            '& .MuiLinearProgress-bar': { backgroundColor: getProgressValue(time) >= 100 ? '#4caf50' : getProgressValue(time) >= 75 ? '#ff9800' : '#f44336',borderRadius: 3,\n                            },\n                          }}\n                        />\n                      )}\n                    </Box>\n                  </TableCell>\n                ))}\n                <TableCell sx={{ fontWeight: \"bold\" }}>{emp.total || \"0h 00m\"}</TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n      </TableContainer>\n      <Typography variant=\"caption\" color=\"gray\" mt={2} display=\"block\">\n        ℹ️ Calculation based on <strong>Time at Work</strong>\n      </Typography>\n    </Box>\n  );\n};\n\nWeekWorkReport.propTypes = {\n  dateRange: PropTypes.shape({\n    startDate: PropTypes.string,\n    endDate: PropTypes.string\n  })\n};\n\nexport default WeekWorkReport;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,SAAS,MAAM,YAAY;AAClC,SACEC,MAAM,EACNC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVC,cAAc,QACT,eAAe;AACtB,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,WAAW,EAAEC,SAAS,QAAQ,UAAU;AAC5E,SAASC,eAAe,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,WAAW,GAAIC,IAAI,IACvBA,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;AAE3C,MAAMC,QAAQ,GAAIC,KAAK,IAAK;EAC1B,IAAIA,KAAK,KAAK,SAAS,EAAE;IAAE,OAAO,KAAK;EAAC;EACxC,IAAIA,KAAK,KAAK,IAAI,EAAE;IAAE,OAAO,MAAM;EAAC;EACpC,MAAMC,KAAK,GAAGC,QAAQ,CAACF,KAAK,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EAC7C,OAAOF,KAAK,IAAI,CAAC,GAAG,OAAO,GAAG,KAAK;AACrC,CAAC;AAED,MAAMG,kBAAkB,GAAIC,OAAO,IAAK;EAAA,IAAAC,iBAAA,EAAAC,mBAAA;EACtC,IAAI,CAACF,OAAO,IAAIA,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,SAAS,EAAE,OAAO,CAAC;;EAEnE;EACA,MAAMG,SAAS,GAAGH,OAAO,CAACI,KAAK,CAAC,gBAAgB,CAAC;EACjD,MAAMC,WAAW,GAAGL,OAAO,CAACI,KAAK,CAAC,kBAAkB,CAAC;EAErD,MAAMR,KAAK,GAAGO,SAAS,aAATA,SAAS,gBAAAF,iBAAA,GAATE,SAAS,CAAEG,MAAM,cAAAL,iBAAA,eAAjBA,iBAAA,CAAmBL,KAAK,GAAGC,QAAQ,CAACM,SAAS,CAACG,MAAM,CAACV,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC;EACjF,MAAMW,OAAO,GAAGF,WAAW,aAAXA,WAAW,gBAAAH,mBAAA,GAAXG,WAAW,CAAEC,MAAM,cAAAJ,mBAAA,eAAnBA,mBAAA,CAAqBK,OAAO,GAAGV,QAAQ,CAACQ,WAAW,CAACC,MAAM,CAACC,OAAO,EAAE,EAAE,CAAC,GAAG,CAAC;EAE3F,OAAQX,KAAK,GAAG,EAAE,GAAIW,OAAO,CAAC,CAAC;AACjC,CAAC;AAED,MAAMC,gBAAgB,GAAIR,OAAO,IAAK;EACpC,MAAMO,OAAO,GAAGR,kBAAkB,CAACC,OAAO,CAAC;EAC3C,MAAMS,eAAe,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;EAChC,OAAOC,IAAI,CAACC,GAAG,CAAEJ,OAAO,GAAGE,eAAe,GAAI,GAAG,EAAE,GAAG,CAAC;AACzD,CAAC;AAED,MAAMG,cAAc,GAAGA,CAAC;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EACxC,MAAMC,QAAQ,GAAGpC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEqC;EAAqB,CAAC,GAAGtC,WAAW,CAAEuC,KAAK,IAAKA,KAAK,CAACC,QAAQ,IAAI;IAAEF,oBAAoB,EAAE;EAAG,CAAC,CAAC;;EAEvG;EACA,MAAMG,WAAW,GAAGH,oBAAoB;EACxC,MAAM,CAACI,QAAQ,EAAEC,WAAW,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;;EAE5C;EACA,MAAMyD,SAAS,GAAGT,SAAS,aAATA,SAAS,eAATA,SAAS,CAAES,SAAS,GAAGzC,QAAQ,CAACgC,SAAS,CAACS,SAAS,CAAC,GAAGvC,WAAW,CAAC,IAAIwC,IAAI,CAAC,CAAC,CAAC;EAChG,MAAMC,OAAO,GAAGX,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEW,OAAO,GAAG3C,QAAQ,CAACgC,SAAS,CAACW,OAAO,CAAC,GAAGxC,SAAS,CAAC,IAAIuC,IAAI,CAAC,CAAC,CAAC;EAExF,MAAME,gBAAgB,GAAG,GAAG7C,MAAM,CAAC0C,SAAS,EAAE,kBAAkB,CAAC,MAAM1C,MAAM,CAAC4C,OAAO,EAAE,kBAAkB,CAAC,EAAE;;EAE5G;EACA5D,SAAS,CAAC,MAAM;IACd,IAAIiD,SAAS,aAATA,SAAS,eAATA,SAAS,CAAES,SAAS,IAAIT,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEW,OAAO,EAAE;MAC9C,MAAME,KAAK,GAAG7C,QAAQ,CAACgC,SAAS,CAACS,SAAS,CAAC;MAC3C,MAAMK,IAAI,GAAG,EAAE;;MAEf;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC1B,MAAMC,GAAG,GAAG/C,OAAO,CAAC4C,KAAK,EAAEE,CAAC,CAAC;QAC7BD,IAAI,CAACG,IAAI,CAAClD,MAAM,CAACiD,GAAG,EAAE,QAAQ,CAAC,CAAC;MAClC;MAEAR,WAAW,CAACM,IAAI,CAAC;;MAEjB;MACAZ,QAAQ,CAAC9B,eAAe,CAAC8C,eAAe,CAAC;QACvCT,SAAS,EAAET,SAAS,CAACS,SAAS;QAC9BE,OAAO,EAAEX,SAAS,CAACW,OAAO;QAC1BQ,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACnB,SAAS,EAAEE,QAAQ,CAAC,CAAC;;EAEzB;EACA,IAAI,CAACI,WAAW,IAAIA,WAAW,CAACc,MAAM,KAAK,CAAC,EAAE;IAC5C,oBACE9C,OAAA,CAACnB,GAAG;MAACkE,CAAC,EAAE,CAAE;MAAAC,QAAA,gBACRhD,OAAA,CAACX,UAAU;QAAC4D,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAClCV;MAAgB;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eACbtD,OAAA,CAACX,UAAU;QAAA2D,QAAA,EAAC;MAA0B;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC;EAEV;EAEA,oBACEtD,OAAA,CAACnB,GAAG;IAACkE,CAAC,EAAE,CAAE;IAAAC,QAAA,gBACRhD,OAAA,CAACnB,GAAG;MAAC0E,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,eAAe;MAACC,UAAU,EAAC,QAAQ;MAACC,EAAE,EAAE,CAAE;MAAAV,QAAA,eAC3EhD,OAAA,CAACX,UAAU;QAAC4D,OAAO,EAAC,IAAI;QAAAD,QAAA,EACrBV;MAAgB;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eACNtD,OAAA,CAACd,cAAc;MAACyE,SAAS,EAAE7E,IAAK;MAAAkE,QAAA,eAC9BhD,OAAA,CAACjB,KAAK;QAAAiE,QAAA,gBACJhD,OAAA,CAACb,SAAS;UAAA6D,QAAA,eACRhD,OAAA,CAACZ,QAAQ;YAAA4D,QAAA,gBACPhD,OAAA,CAACf,SAAS;cAAA+D,QAAA,eAAChD,OAAA;gBAAAgD,QAAA,EAAQ;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,EAC3CrB,QAAQ,CAAC7B,GAAG,CAAC,CAACsC,GAAG,EAAED,CAAC,kBACnBzC,OAAA,CAACf,SAAS;cAAS2E,KAAK,EAAC,QAAQ;cAAAZ,QAAA,EAAEN;YAAG,GAAtBD,CAAC;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAiC,CACnD,CAAC,eACFtD,OAAA,CAACf,SAAS;cAAA+D,QAAA,eAAChD,OAAA;gBAAAgD,QAAA,EAAQ;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZtD,OAAA,CAAChB,SAAS;UAAAgE,QAAA,EACPhB,WAAW,CAAC5B,GAAG,CAAC,CAACyD,GAAG,EAAEpB,CAAC,kBACtBzC,OAAA,CAACZ,QAAQ;YAAA4D,QAAA,gBACPhD,OAAA,CAACf,SAAS;cAAA+D,QAAA,eACRhD,OAAA,CAACnB,GAAG;gBAAC0E,OAAO,EAAC,MAAM;gBAACE,UAAU,EAAC,QAAQ;gBAACK,GAAG,EAAE,CAAE;gBAAAd,QAAA,gBAC7ChD,OAAA,CAACpB,MAAM;kBAAAoE,QAAA,EAAE/C,WAAW,CAAC4D,GAAG,CAAC3D,IAAI,IAAI,EAAE;gBAAC;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,eAC9CtD,OAAA,CAACX,UAAU;kBAAA2D,QAAA,EAAEa,GAAG,CAAC3D,IAAI,IAAI;gBAAE;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,EACX,CAACO,GAAG,CAACE,QAAQ,IAAIC,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE7D,GAAG,CAAC,CAAC8D,IAAI,EAAEC,GAAG,kBACnDnE,OAAA,CAACf,SAAS;cAAW2E,KAAK,EAAC,QAAQ;cAACQ,EAAE,EAAE;gBAAEC,QAAQ,EAAE;cAAI,CAAE;cAAArB,QAAA,eACxDhD,OAAA,CAACnB,GAAG;gBAAAmE,QAAA,gBACFhD,OAAA,CAACX,UAAU;kBACT4D,OAAO,EAAC,OAAO;kBACfmB,EAAE,EAAE;oBAAEE,KAAK,EAAE/D,QAAQ,CAAC2D,IAAI,CAAC;oBAAEK,UAAU,EAAE,MAAM;oBAAEb,EAAE,EAAE;kBAAI,CAAE;kBAAAV,QAAA,EAE1DkB;gBAAI;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,EACZY,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,SAAS,iBAClClE,OAAA,CAACV,cAAc;kBACb2D,OAAO,EAAC,aAAa;kBACrBzC,KAAK,EAAEa,gBAAgB,CAAC6C,IAAI,CAAE;kBAC9BE,EAAE,EAAE;oBACFI,MAAM,EAAE,CAAC;oBACTC,YAAY,EAAE,CAAC;oBACfC,eAAe,EAAE,oBAAoB;oBACrC,0BAA0B,EAAE;sBAAEA,eAAe,EAAErD,gBAAgB,CAAC6C,IAAI,CAAC,IAAI,GAAG,GAAG,SAAS,GAAG7C,gBAAgB,CAAC6C,IAAI,CAAC,IAAI,EAAE,GAAG,SAAS,GAAG,SAAS;sBAACO,YAAY,EAAE;oBAC9J;kBACF;gBAAE;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC,GArBQa,GAAG;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsBR,CACZ,CAAC,eACFtD,OAAA,CAACf,SAAS;cAACmF,EAAE,EAAE;gBAAEG,UAAU,EAAE;cAAO,CAAE;cAAAvB,QAAA,EAAEa,GAAG,CAACc,KAAK,IAAI;YAAQ;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA,GAhC7Db,CAAC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiCN,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eACjBtD,OAAA,CAACX,UAAU;MAAC4D,OAAO,EAAC,SAAS;MAACqB,KAAK,EAAC,MAAM;MAACM,EAAE,EAAE,CAAE;MAACrB,OAAO,EAAC,OAAO;MAAAP,QAAA,GAAC,oCACxC,eAAAhD,OAAA;QAAAgD,QAAA,EAAQ;MAAY;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAAC3B,EAAA,CAhHIF,cAAc;EAAA,QACDjC,WAAW,EACKD,WAAW;AAAA;AAAAsF,EAAA,GAFxCpD,cAAc;AAkHpBA,cAAc,CAACqD,SAAS,GAAG;EACzBpD,SAAS,EAAE/C,SAAS,CAACoG,KAAK,CAAC;IACzB5C,SAAS,EAAExD,SAAS,CAACqG,MAAM;IAC3B3C,OAAO,EAAE1D,SAAS,CAACqG;EACrB,CAAC;AACH,CAAC;AAED,eAAevD,cAAc;AAAC,IAAAoD,EAAA;AAAAI,YAAA,CAAAJ,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}