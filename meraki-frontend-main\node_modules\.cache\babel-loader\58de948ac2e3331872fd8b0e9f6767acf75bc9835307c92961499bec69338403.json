{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\Dashboard\\\\components\\\\Activity.js\",\n  _s = $RefreshSig$();\n/**\r\n * Activity Component - User's daily activity tracking interface\r\n */\n\nimport React, { useEffect, useState, createContext } from \"react\";\nimport Can from \"../../../utils/can\";\nimport { actions, features } from \"../../../constants/permission\";\nimport { Box, Button, Card, Grid, Typography, MenuItem, Select, FormControl, CircularProgress } from \"@mui/material\";\nimport { Timer, AssignmentIndOutlined } from \"@mui/icons-material\";\nimport moment from \"moment\";\nimport { ActivityActions, AttendanceActions, GeneralActions } from \"../../../slices/actions\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { AttendanceSelector, UserSelector } from \"../../../selectors\";\nimport { ActivitySelector } from \"selectors/ActivitySelector\";\nimport { LeaveSelector } from \"selectors/LeaveSelector\";\nimport { SettingSelector } from \"selectors/SettingSelector\";\nimport { styled, useTheme } from \"@mui/material/styles\";\nimport TodayGoal from \"./TodayGoal\";\nimport OtherBreak from \"./BreakReasone\";\nimport EarlyLate from \"./EarlyLate\";\nimport OverLimitBreak from \"./OverLimitBreak\";\nimport WorkHoursStatus from \"./WorkHoursStatus\";\nimport ProductivityChart from \"./ProductivityChart\";\nimport AttendanceBarChart from \"./AttendanceBarChart\";\nimport Widget from \"./Widget\";\n\n// Utility functions for timezone handling\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst getLocalDateString = (date = new Date()) => {\n  const year = date.getFullYear();\n  const month = String(date.getMonth() + 1).padStart(2, '0');\n  const day = String(date.getDate()).padStart(2, '0');\n  return `${year}-${month}-${day}`;\n};\nconst isSameLocalDate = (date1, date2) => {\n  if (!date1 || !date2) return false;\n  const d1 = new Date(date1);\n  const d2 = new Date(date2);\n  return d1.toDateString() === d2.toDateString();\n};\nconst isToday = date => {\n  if (!date) return false;\n  const today = new Date();\n  const checkDate = new Date(date);\n  return checkDate.toDateString() === today.toDateString();\n};\n\n// Context for sharing activity data with child components\nexport const activityContext = /*#__PURE__*/createContext();\nconst InfoLog = styled(Box)(() => ({\n  display: \"flex\",\n  justifyContent: \"space-between\"\n}));\n_c = InfoLog;\nexport default function Activity() {\n  _s();\n  var _todayActivity$2, _setting$leaveLimit;\n  const theme = useTheme();\n  const dispatch = useDispatch();\n  const profile = useSelector(UserSelector.profile());\n  const attendances = useSelector(AttendanceSelector.getAttendances());\n  const activities = useSelector(ActivitySelector.getActivityHistory());\n  const countLeave = useSelector(LeaveSelector.countLeave());\n  const setting = useSelector(SettingSelector.getSetting());\n\n  // State variables\n  const [controller, setController] = useState(false);\n  const [lunchController, setLunch] = useState(false);\n  const [lunchType, setLunchType] = useState(localStorage.getItem(\"lunchType\") || \"\");\n  const [showBreakPop, setShowBreakPop] = useState(false);\n  const [lateCheckIn, setLateCheckIn] = useState(false);\n  const [earlyCheckOut, setEarlyCheckOut] = useState(false);\n  const [todayStatus, setTodayStatus] = useState(false);\n  const [overLimitBreak, setOverLimitBreak] = useState(false);\n  const [slotController, setSlotController] = useState(true);\n  const [todayActivity, setActivity] = useState([]);\n  const [attendance, setAttendance] = useState({});\n  const [showGoalPopup, setShowGoalPopup] = useState(false);\n  const [workHoursStatus, setWorkHoursStatus] = useState(null);\n  const [showWorkHoursStatus, setShowWorkHoursStatus] = useState(false);\n  const [checkingIn, setCheckingIn] = useState(false);\n  const [checkingOut, setCheckingOut] = useState(false);\n\n  // Handle break actions based on lunch type selection\n  useEffect(() => {\n    var _todayActivity$;\n    if (todayActivity.length > 0 && (_todayActivity$ = todayActivity[0]) !== null && _todayActivity$ !== void 0 && _todayActivity$._id && profile && profile._id && attendance && attendance._id) {\n      switch (lunchType) {\n        case \"lunchBreak\":\n          setLunch(!lunchController);\n          dispatch(AttendanceActions.createLunchBreak({\n            id: attendance._id,\n            lunchIn: new Date()\n          }));\n          dispatch(ActivityActions.breakStartRed({\n            _id: todayActivity[0]._id,\n            type: lunchType,\n            breakStart: new Date().setMilliseconds(0),\n            description: \"Lunch Break\",\n            user: profile._id\n          }));\n          setSlotController(false);\n          break;\n        case \"teaBreak\":\n          setLunch(!lunchController);\n          dispatch(AttendanceActions.createLunchBreak({\n            id: attendance._id,\n            lunchIn: new Date()\n          }));\n          dispatch(ActivityActions.breakStartRed({\n            _id: todayActivity[0]._id,\n            breakStart: new Date().setMilliseconds(0),\n            type: lunchType,\n            description: \"Tea Break\",\n            user: profile._id\n          }));\n          setSlotController(false);\n          break;\n        case \"other\":\n          setLunch(!lunchController);\n          dispatch(AttendanceActions.createLunchBreak({\n            id: attendance._id,\n            lunchIn: new Date().setMilliseconds(0),\n            user: profile._id\n          }));\n          setShowBreakPop(true);\n          setSlotController(false);\n          break;\n        case \"breakOut\":\n          setLunch(!lunchController);\n          dispatch(AttendanceActions.updateLunchBreak({\n            id: attendance._id,\n            lunchOut: new Date()\n          }));\n          dispatch(ActivityActions.breakEndRed({\n            _id: todayActivity[0]._id,\n            breakEnd: new Date().setMilliseconds(0),\n            type: lunchType,\n            user: profile._id\n          }));\n          setSlotController(true);\n          break;\n        default:\n          break;\n      }\n    }\n  }, [lunchType]);\n\n  // Process activity data to find today's activity\n  useEffect(() => {\n    if (activities && activities.length > 0) {\n      console.log('Processing activities to find today\\'s activity:', activities.length);\n\n      // Find today's activity using the utility function\n      const todayActivity = activities.find(activity => {\n        if (!activity.checkInTime) {\n          console.log('Activity has no checkInTime:', activity._id);\n          return false;\n        }\n        const isActivityToday = isToday(activity.checkInTime);\n        console.log(`Activity ${activity._id}: checkInTime=${activity.checkInTime}, isToday=${isActivityToday}`);\n        return isActivityToday;\n      });\n      console.log(`Found today's activity:`, todayActivity ? 'Yes' : 'No', todayActivity === null || todayActivity === void 0 ? void 0 : todayActivity._id);\n      if (todayActivity) {\n        setActivity([todayActivity]);\n        setShowGoalPopup(false);\n        sessionStorage.setItem('hasShownGoalPopup', 'true');\n      } else {\n        // No activity found for today\n        setActivity([]);\n      }\n    }\n  }, [activities]);\n\n  // Handle popup displays for various activity statuses\n  useEffect(() => {\n    if (todayActivity.length > 0) {\n      const hasShownLateCheckInPopup = sessionStorage.getItem('hasShownLateCheckInPopup');\n      const hasShownEarlyCheckOutPopup = sessionStorage.getItem('hasShownEarlyCheckOutPopup');\n      const hasShownTodayStatusPopup = sessionStorage.getItem('hasShownTodayStatusPopup');\n      const hasShownOverLimitBreakPopup = sessionStorage.getItem('hasShownOverLimitBreakPopup');\n\n      // Handle late check-in popup\n      if (todayActivity[0].lateCheckInStatus && !todayActivity.some(obj => Object.prototype.hasOwnProperty.call(obj, \"lateCheckInDiscription\")) && !hasShownLateCheckInPopup) {\n        setLateCheckIn(true);\n        sessionStorage.setItem('hasShownLateCheckInPopup', 'true');\n      }\n\n      // Handle early check-out popup\n      if (todayActivity[0].earlyCheckOutStatus && !todayActivity.some(obj => Object.prototype.hasOwnProperty.call(obj, \"earlyCheckOutDiscription\")) && !hasShownEarlyCheckOutPopup) {\n        setEarlyCheckOut(true);\n        sessionStorage.setItem('hasShownEarlyCheckOutPopup', 'true');\n      }\n\n      // Handle today's status popup (after checkout)\n      if (todayActivity.some(obj => Object.prototype.hasOwnProperty.call(obj, \"checkOutTime\")) && !todayActivity.some(obj => Object.prototype.hasOwnProperty.call(obj, \"workStatus\")) && !hasShownTodayStatusPopup) {\n        setTodayStatus(true);\n        sessionStorage.setItem('hasShownTodayStatusPopup', 'true');\n      }\n\n      // Handle over-limit break popup\n      if (todayActivity[0].breaksHistory && todayActivity[0].overLimitBreakStatus === true && !hasShownOverLimitBreakPopup) {\n        setOverLimitBreak(true);\n        sessionStorage.setItem('hasShownOverLimitBreakPopup', 'true');\n      }\n    }\n  }, [todayActivity]);\n\n  // Fetch initial attendance and activity data\n  useEffect(() => {\n    if (Can(actions.read, features.attendance) && profile && profile._id) {\n      // Get the current date in local timezone for consistent date handling\n      const today = new Date();\n      const formattedDate = today.toISOString().split('T')[0]; // This gives YYYY-MM-DD in UTC\n\n      // For better timezone handling, let's use local date\n      const localYear = today.getFullYear();\n      const localMonth = String(today.getMonth() + 1).padStart(2, '0');\n      const localDay = String(today.getDate()).padStart(2, '0');\n      const localFormattedDate = `${localYear}-${localMonth}-${localDay}`;\n      console.log(`Fetching data for date: ${localFormattedDate} (local timezone)`);\n\n      // Clear session storage flags at the start of a new day\n      const lastLoginDate = localStorage.getItem('lastLoginDate');\n      if (lastLoginDate !== localFormattedDate) {\n        sessionStorage.removeItem('hasShownGoalPopup');\n        sessionStorage.removeItem('hasShownLateCheckInPopup');\n        sessionStorage.removeItem('hasShownEarlyCheckOutPopup');\n        sessionStorage.removeItem('hasShownTodayStatusPopup');\n        sessionStorage.removeItem('hasShownOverLimitBreakPopup');\n        localStorage.setItem('lastLoginDate', localFormattedDate);\n      }\n      dispatch(AttendanceActions.getAttendances({\n        user: profile._id,\n        date: localFormattedDate // Use local date for consistency\n      }));\n      dispatch(ActivityActions.getUserActivity({\n        id: profile._id\n      }));\n    }\n  }, [profile, dispatch]);\n\n  // Update local attendance state when attendance data changes\n  useEffect(() => {\n    // Check if the attendance data is for today using local timezone\n    const today = new Date();\n    const todayDateString = today.toDateString(); // Local timezone date string\n\n    console.log(`Looking for attendance on: ${todayDateString}`);\n    if (attendances.length > 0) {\n      // Filter for today's attendance only using local timezone comparison\n      const todayAttendances = attendances.filter(att => {\n        if (!att.checkIn) return false;\n        const attDate = new Date(att.checkIn);\n        const attDateString = attDate.toDateString(); // Convert UTC to local timezone\n\n        console.log(`Attendance date: ${attDateString}, matches today: ${attDateString === todayDateString}`);\n        return attDateString === todayDateString;\n      });\n      console.log(`Found ${todayAttendances.length} attendance records for today`);\n      if (todayAttendances.length > 0) {\n        setAttendance(todayAttendances[0]);\n      } else {\n        // No attendance for today\n        setAttendance({});\n      }\n    } else {\n      setAttendance({});\n    }\n  }, [attendances]);\n\n  // Refresh attendance data after check-in or break actions\n  useEffect(() => {\n    if (Can(actions.read, features.attendance) && profile && profile._id) {\n      // Get local date for consistent timezone handling\n      const today = new Date();\n      const localYear = today.getFullYear();\n      const localMonth = String(today.getMonth() + 1).padStart(2, '0');\n      const localDay = String(today.getDate()).padStart(2, '0');\n      const localFormattedDate = `${localYear}-${localMonth}-${localDay}`;\n\n      // Force cache refresh by adding a timestamp\n      const timestamp = Date.now();\n      console.log(`Refreshing attendance data for date: ${localFormattedDate}`);\n      dispatch(AttendanceActions.getAttendances({\n        user: profile._id,\n        date: localFormattedDate,\n        // Use local date\n        _t: timestamp // Add timestamp to prevent caching\n      }));\n    }\n    dispatch(GeneralActions.removeSuccess(AttendanceActions.createAttendance.type));\n  }, [controller, lunchController, profile, dispatch]);\n\n  // Persist lunch type to localStorage\n  useEffect(() => {\n    localStorage.setItem(\"lunchType\", lunchType);\n  }, [lunchType]);\n\n  // Handle user check-in action\n  const handleCheckIn = () => {\n    if (checkingIn) {\n      return;\n    }\n    console.log(\"check in clicked\");\n\n    // Clear any existing attendance data to ensure fresh check-in\n    setAttendance({});\n\n    // Reset session storage for a new check-in using local date\n    const today = new Date();\n    const localYear = today.getFullYear();\n    const localMonth = String(today.getMonth() + 1).padStart(2, '0');\n    const localDay = String(today.getDate()).padStart(2, '0');\n    const localFormattedDate = `${localYear}-${localMonth}-${localDay}`;\n    const lastCheckInDate = localStorage.getItem('lastCheckInDate');\n    console.log(`Check-in for date: ${localFormattedDate}`);\n\n    // If it's a new day, clear all session storage flags\n    if (lastCheckInDate !== localFormattedDate) {\n      sessionStorage.removeItem('hasShownGoalPopup');\n      sessionStorage.removeItem('hasShownLateCheckInPopup');\n      sessionStorage.removeItem('hasShownEarlyCheckOutPopup');\n      sessionStorage.removeItem('hasShownTodayStatusPopup');\n      sessionStorage.removeItem('hasShownOverLimitBreakPopup');\n      localStorage.setItem('lastCheckInDate', localFormattedDate);\n    }\n\n    // Show goal popup - the actual check-in will happen after goal submission\n    setShowGoalPopup(true);\n  };\n  const handleSubmitGoal = goalData => {\n    console.log(\"Goal submitted:\", goalData);\n\n    // Set checking in state\n    setCheckingIn(true);\n\n    // Close the goal popup\n    setShowGoalPopup(false);\n\n    // Mark as shown to prevent re-showing\n    sessionStorage.setItem('hasShownGoalPopup', 'true');\n\n    // Check if profile exists\n    if (!profile || !profile._id) {\n      console.error(\"Profile data is missing\");\n      setCheckingIn(false);\n      return;\n    }\n\n    // Create the goal first\n    if (goalData.todaysGoal) {\n      dispatch(ActivityActions.createTodayGoal({\n        id: profile._id,\n        todaysGoal: goalData.todaysGoal\n      }));\n    }\n\n    // Then perform check-in with current timestamp\n    const checkInTime = new Date();\n    console.log(`Check-in time: ${checkInTime.toISOString()} (UTC), Local: ${checkInTime.toString()}`);\n    dispatch(AttendanceActions.createAttendance({\n      user: profile._id,\n      checkIn: checkInTime\n    }));\n\n    // Force immediate refresh of attendance data with local date\n    const today = new Date();\n    const localYear = today.getFullYear();\n    const localMonth = String(today.getMonth() + 1).padStart(2, '0');\n    const localDay = String(today.getDate()).padStart(2, '0');\n    const localFormattedDate = `${localYear}-${localMonth}-${localDay}`;\n    const timestamp = Date.now();\n\n    // Store the check-in date in localStorage using local date\n    localStorage.setItem('lastCheckInDate', localFormattedDate);\n    console.log(`Refreshing attendance data for local date: ${localFormattedDate}`);\n\n    // Add a small delay before fetching updated attendance data\n    setTimeout(() => {\n      dispatch(AttendanceActions.getAttendances({\n        user: profile._id,\n        date: localFormattedDate,\n        // Use local date\n        _t: timestamp // Add timestamp to prevent caching\n      }));\n\n      // Update controller state\n      setController(!controller);\n\n      // Reset checking state\n      setCheckingIn(false);\n    }, 1000);\n  };\n  // Handle user check-out action\n  const handleCheckOut = () => {\n    if (checkingOut) {\n      return;\n    }\n\n    // Check if profile and attendance data exist\n    if (!profile || !profile._id || !attendance || !attendance._id || !todayActivity || !todayActivity.length) {\n      console.error(\"Missing required data for check-out\");\n      return;\n    }\n    setCheckingOut(true);\n    setController(!controller);\n    dispatch(AttendanceActions.updateAttendance({\n      id: attendance._id,\n      checkOut: new Date()\n    }));\n    dispatch(ActivityActions.checkOutStatusUpdate({\n      _id: todayActivity[0]._id,\n      user: profile._id\n    }));\n\n    // Clear session storage flags when checking out\n    sessionStorage.removeItem('hasShownGoalPopup');\n    sessionStorage.removeItem('hasShownLateCheckInPopup');\n    sessionStorage.removeItem('hasShownEarlyCheckOutPopup');\n    sessionStorage.removeItem('hasShownTodayStatusPopup');\n    sessionStorage.removeItem('hasShownOverLimitBreakPopup');\n    setTimeout(() => {\n      setCheckingOut(false);\n    }, 2000);\n  };\n\n  // Listen for checkout success to show work hours status\n  const checkoutSuccess = useSelector(state => state.general.success.find(s => s.action === ActivityActions.checkOutStatusUpdate.type));\n\n  // Update work hours status when checkout is successful\n  useEffect(() => {\n    if (checkoutSuccess && checkoutSuccess.data) {\n      setWorkHoursStatus(checkoutSuccess.data);\n      setShowWorkHoursStatus(true);\n      setTimeout(() => {\n        dispatch(GeneralActions.removeSuccess(ActivityActions.checkOutStatusUpdate.type));\n      }, 500);\n    }\n  }, [checkoutSuccess, dispatch]);\n\n  // Handle break type selection\n  const handleBreakOut = event => {\n    setLunchType(event.target.value);\n  };\n\n  // Control the visibility of the break reason popup\n  function breakPopUpController(val) {\n    setShowBreakPop(val);\n  }\n\n  // Check if user has permission to view attendance\n  if (!Can(actions.readSelf, features.attendance)) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 544,\n      columnNumber: 12\n    }, this);\n  }\n\n  // UI\n  return /*#__PURE__*/_jsxDEV(Grid, {\n    container: true,\n    spacing: 3,\n    sx: {\n      padding: \"20px\",\n      display: \"flex\",\n      alignItems: \"center\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      md: 8,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          height: \"100%\",\n          padding: \"20px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(ProductivityChart, {\n          todayActivities: todayActivity\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 553,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 552,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 551,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      md: 4,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          height: \"100%\",\n          display: \"flex\",\n          flexDirection: \"column\",\n          alignItems: \"center\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Timer, {\n          color: \"primary\",\n          sx: {\n            fontSize: 100\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 567,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            align: \"center\",\n            gutterBottom: true,\n            sx: {\n              padding: \"10px\",\n              margin: \"10px\"\n            },\n            children: \"Your Activity Today\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(InfoLog, {\n            children: [\"Check In\", \"Check Out\"].map((label, index) => /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: \"center\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                children: label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 577,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                children: attendance[index ? \"checkOut\" : \"checkIn\"] ? moment(attendance[index ? \"checkOut\" : \"checkIn\"]).format(\"HH:mm\") : \"-\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 578,\n                columnNumber: 19\n              }, this)]\n            }, label, true, {\n              fileName: _jsxFileName,\n              lineNumber: 576,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 574,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 569,\n          columnNumber: 11\n        }, this), !attendance.checkOut && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3\n          },\n          children: !attendance.checkIn ? /*#__PURE__*/_jsxDEV(Button, {\n            fullWidth: true,\n            variant: \"contained\",\n            onClick: handleCheckIn,\n            disabled: checkingIn,\n            sx: {\n              backgroundColor: checkingIn ? \"#a5d6a7\" : \"green\",\n              color: \"white\",\n              fontWeight: \"bold\",\n              borderRadius: \"8px\",\n              width: \"150px\",\n              height: \"50px\",\n              boxShadow: \"0px 4px 6px rgba(0, 0, 0, 0.2)\",\n              display: \"flex\",\n              alignItems: \"center\",\n              \"&:hover\": {\n                backgroundColor: checkingIn ? \"#a5d6a7\" : \"darkgreen\"\n              }\n            },\n            children: checkingIn ? \"Processing...\" : \"Check In\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 590,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"flex\",\n              justifyContent: \"space-between\",\n              alignItems: \"center\",\n              width: \"100%\",\n              marginTop: \"10px\",\n              marginBottom: \"10px\",\n              gap: \"10px\"\n            },\n            children: [todayActivity.length > 0 && (_todayActivity$2 = todayActivity[0]) !== null && _todayActivity$2 !== void 0 && _todayActivity$2.breakStatus ? /*#__PURE__*/_jsxDEV(FormControl, {\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                labelId: \"break-label\",\n                value: lunchType,\n                label: \"Break Type\",\n                onChange: handleBreakOut,\n                sx: {\n                  backgroundColor: \"#2e7d32\",\n                  color: \"white\",\n                  fontWeight: \"bold\",\n                  borderRadius: \"8px\",\n                  width: \"150px\",\n                  height: \"50px\",\n                  boxShadow: \"0px 4px 6px rgba(0, 0, 0, 0.2)\",\n                  display: \"flex\",\n                  alignItems: \"center\"\n                },\n                renderValue: () => \"----\",\n                children: /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"breakOut\",\n                  children: \"Break Out\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 643,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 625,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(FormControl, {\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                labelId: \"break-label\",\n                value: lunchType || \"\",\n                label: \"Break Type\",\n                onChange: handleBreakOut,\n                sx: {\n                  backgroundColor: \"#2e7d32\",\n                  color: \"white\",\n                  fontWeight: \"bold\",\n                  borderRadius: \"8px\",\n                  width: \"150px\",\n                  height: \"50px\",\n                  boxShadow: \"0px 4px 6px rgba(0, 0, 0, 0.2)\",\n                  display: \"flex\",\n                  alignItems: \"center\"\n                },\n                renderValue: () => \"Break In\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  disabled: true,\n                  children: \"Break In\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 666,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"lunchBreak\",\n                  children: \"Lunch Break\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 667,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"teaBreak\",\n                  children: \"Tea Break\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 668,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"other\",\n                  children: \"Other\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 669,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 648,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 647,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"error\",\n              onClick: handleCheckOut,\n              disabled: checkingOut,\n              sx: {\n                backgroundColor: checkingOut ? \"#ef9a9a\" : \"darked\",\n                color: \"white\",\n                fontWeight: \"bold\",\n                borderRadius: \"8px\",\n                width: \"150px\",\n                height: \"50px\",\n                boxShadow: \"0px 4px 6px rgba(0, 0, 0, 0.2)\",\n                display: \"flex\",\n                alignItems: \"center\"\n              },\n              children: checkingOut ? \"Processing...\" : \"Check Out\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 675,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 611,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 587,\n          columnNumber: 13\n        }, this), showGoalPopup && todayActivity.length === 0 && !sessionStorage.getItem('hasShownGoalPopup') && /*#__PURE__*/_jsxDEV(TodayGoal, {\n          title: \"Today's Goal\",\n          task: \"goal\",\n          onSubmit: handleSubmitGoal // Fixed typo\n          ,\n          onClose: () => {\n            setShowGoalPopup(false);\n            sessionStorage.setItem('hasShownGoalPopup', 'true');\n          },\n          required: true // Make goal mandatory\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 701,\n          columnNumber: 3\n        }, this), showBreakPop && /*#__PURE__*/_jsxDEV(OtherBreak, {\n          openVal: true,\n          settingFun: breakPopUpController,\n          _id: todayActivity[0]._id,\n          profile: profile\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 715,\n          columnNumber: 13\n        }, this), lateCheckIn && /*#__PURE__*/_jsxDEV(EarlyLate, {\n          openVal: true,\n          task: \"late\",\n          dialogTitle: \"Late Check In\",\n          id: todayActivity[0]._id,\n          onClose: () => {\n            sessionStorage.setItem('hasShownLateCheckInPopup', 'true');\n            setLateCheckIn(false);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 724,\n          columnNumber: 13\n        }, this), todayStatus && /*#__PURE__*/_jsxDEV(EarlyLate, {\n          openVal: true,\n          task: \"status\",\n          dialogTitle: \"Work Status\",\n          id: todayActivity[0]._id,\n          onClose: () => {\n            sessionStorage.setItem('hasShownTodayStatusPopup', 'true');\n            setTodayStatus(false);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 737,\n          columnNumber: 13\n        }, this), earlyCheckOut && /*#__PURE__*/_jsxDEV(EarlyLate, {\n          openVal: true,\n          task: \"early\",\n          dialogTitle: \"Early Check Out\",\n          id: todayActivity[0]._id,\n          onClose: () => {\n            sessionStorage.setItem('hasShownEarlyCheckOutPopup', 'true');\n            setEarlyCheckOut(false);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 750,\n          columnNumber: 13\n        }, this), overLimitBreak && /*#__PURE__*/_jsxDEV(OverLimitBreak, {\n          openVal: true,\n          id: todayActivity[0]._id,\n          onClose: () => {\n            sessionStorage.setItem('hasShownOverLimitBreakPopup', 'true');\n            setOverLimitBreak(false);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 763,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(WorkHoursStatus, {\n          open: showWorkHoursStatus,\n          statusData: workHoursStatus,\n          onClose: () => setShowWorkHoursStatus(false)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 774,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 559,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 558,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(AttendanceBarChart, {\n            activities: activities\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 787,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 786,\n          columnNumber: 11\n        }, this), Can(actions.readSelf, features.leave) && /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Widget, {\n              title: \"Leave Taken\",\n              content: countLeave !== null && countLeave !== void 0 ? countLeave : 0,\n              icon: /*#__PURE__*/_jsxDEV(AssignmentIndOutlined, {\n                sx: {\n                  color: theme.palette.primary.main,\n                  fontSize: 62\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 797,\n                columnNumber: 25\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 794,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 793,\n            columnNumber: 15\n          }, this), Can(actions.read, features.attendance) && /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Widget, {\n              title: \"Quote Leave\",\n              content: (_setting$leaveLimit = setting === null || setting === void 0 ? void 0 : setting.leaveLimit) !== null && _setting$leaveLimit !== void 0 ? _setting$leaveLimit : 0,\n              icon: /*#__PURE__*/_jsxDEV(AssignmentIndOutlined, {\n                sx: {\n                  color: theme.palette.primary.main,\n                  fontSize: 62\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 805,\n                columnNumber: 27\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 802,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 801,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 792,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 784,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 783,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 549,\n    columnNumber: 5\n  }, this);\n}\n_s(Activity, \"ExyqxY/kRmZjH5vxui8n+2GKMlQ=\", false, function () {\n  return [useTheme, useDispatch, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector];\n});\n_c2 = Activity;\nvar _c, _c2;\n$RefreshReg$(_c, \"InfoLog\");\n$RefreshReg$(_c2, \"Activity\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "createContext", "Can", "actions", "features", "Box", "<PERSON><PERSON>", "Card", "Grid", "Typography", "MenuItem", "Select", "FormControl", "CircularProgress", "Timer", "AssignmentIndOutlined", "moment", "ActivityActions", "AttendanceActions", "GeneralActions", "useDispatch", "useSelector", "AttendanceSelector", "UserSelector", "ActivitySelector", "LeaveSelector", "SettingSelector", "styled", "useTheme", "TodayGoal", "OtherBreak", "EarlyLate", "OverLimitBreak", "WorkHoursStatus", "ProductivityChart", "AttendanceBarChart", "Widget", "jsxDEV", "_jsxDEV", "getLocalDateString", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "isSameLocalDate", "date1", "date2", "d1", "d2", "toDateString", "isToday", "today", "checkDate", "activityContext", "InfoLog", "display", "justifyContent", "_c", "Activity", "_s", "_todayActivity$2", "_setting$leaveLimit", "theme", "dispatch", "profile", "attendances", "getAttendances", "activities", "getActivityHistory", "count<PERSON><PERSON>ve", "setting", "getSetting", "controller", "setController", "lunchController", "setLunch", "lunchType", "setLunchType", "localStorage", "getItem", "showBreakPop", "setShowBreakPop", "lateCheckIn", "setLateCheckIn", "earlyCheckOut", "setEarlyCheckOut", "todayStatus", "setTodayStatus", "overLimitBreak", "setOverLimitBreak", "slotController", "setSlotController", "todayActivity", "setActivity", "attendance", "setAttendance", "showGoalPopup", "setShowGoalPopup", "workHoursStatus", "setWorkHoursStatus", "showWorkHoursStatus", "setShowWorkHoursStatus", "checkingIn", "setCheckingIn", "checkingOut", "setCheckingOut", "_todayActivity$", "length", "_id", "createLunchBreak", "id", "lunchIn", "breakStartRed", "type", "breakStart", "setMilliseconds", "description", "user", "updateLunchBreak", "lunchOut", "breakEndRed", "breakEnd", "console", "log", "find", "activity", "checkInTime", "isActivityToday", "sessionStorage", "setItem", "hasShownLateCheckInPopup", "hasShownEarlyCheckOutPopup", "hasShownTodayStatusPopup", "hasShownOverLimitBreakPopup", "lateCheckInStatus", "some", "obj", "Object", "prototype", "hasOwnProperty", "call", "earlyCheckOutStatus", "breaksHistory", "overLimitBreakStatus", "read", "formattedDate", "toISOString", "split", "localYear", "localMonth", "localDay", "localFormattedDate", "lastLoginDate", "removeItem", "getUserActivity", "todayDateString", "todayAttendances", "filter", "att", "checkIn", "attDate", "attDateString", "timestamp", "now", "_t", "removeSuccess", "createAttendance", "handleCheckIn", "lastCheckInDate", "handleSubmitGoal", "goalData", "error", "todaysGoal", "createTodayGoal", "toString", "setTimeout", "handleCheckOut", "updateAttendance", "checkOut", "checkOutStatusUpdate", "checkoutSuccess", "state", "general", "success", "s", "action", "data", "handleBreakOut", "event", "target", "value", "breakPopUpController", "val", "readSelf", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "spacing", "sx", "padding", "alignItems", "children", "item", "xs", "md", "height", "todayActivities", "flexDirection", "color", "fontSize", "variant", "align", "gutterBottom", "margin", "map", "label", "index", "textAlign", "format", "mt", "fullWidth", "onClick", "disabled", "backgroundColor", "fontWeight", "borderRadius", "width", "boxShadow", "style", "marginTop", "marginBottom", "gap", "breakStatus", "labelId", "onChange", "renderValue", "title", "task", "onSubmit", "onClose", "required", "openVal", "<PERSON><PERSON><PERSON>", "dialogTitle", "open", "statusData", "leave", "content", "icon", "palette", "primary", "main", "leaveLimit", "_c2", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/Dashboard/components/Activity.js"], "sourcesContent": ["/**\r\n * Activity Component - User's daily activity tracking interface\r\n */\r\n\r\nimport React, { useEffect, useState, createContext } from \"react\";\r\nimport Can from \"../../../utils/can\";\r\nimport { actions, features } from \"../../../constants/permission\";\r\nimport {\r\n  Box,\r\n  Button,\r\n  Card,\r\n  Grid,\r\n  Typography,\r\n  MenuItem,\r\n  Select,\r\n  FormControl,\r\n  CircularProgress,\r\n} from \"@mui/material\";\r\nimport { Timer, AssignmentIndOutlined } from \"@mui/icons-material\";\r\nimport moment from \"moment\";\r\nimport {\r\n  ActivityActions,\r\n  AttendanceActions,\r\n  GeneralActions,\r\n} from \"../../../slices/actions\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { AttendanceSelector, UserSelector } from \"../../../selectors\";\r\nimport { ActivitySelector } from \"selectors/ActivitySelector\";\r\nimport { LeaveSelector } from \"selectors/LeaveSelector\";\r\nimport { SettingSelector } from \"selectors/SettingSelector\";\r\nimport { styled, useTheme } from \"@mui/material/styles\";\r\nimport TodayGoal from \"./TodayGoal\";\r\nimport OtherBreak from \"./BreakReasone\";\r\nimport EarlyLate from \"./EarlyLate\";\r\nimport OverLimitBreak from \"./OverLimitBreak\";\r\nimport WorkHoursStatus from \"./WorkHoursStatus\";\r\nimport ProductivityChart from \"./ProductivityChart\";\r\nimport AttendanceBarChart from \"./AttendanceBarChart\";\r\nimport Widget from \"./Widget\";\r\n\r\n// Utility functions for timezone handling\r\nconst getLocalDateString = (date = new Date()) => {\r\n  const year = date.getFullYear();\r\n  const month = String(date.getMonth() + 1).padStart(2, '0');\r\n  const day = String(date.getDate()).padStart(2, '0');\r\n  return `${year}-${month}-${day}`;\r\n};\r\n\r\nconst isSameLocalDate = (date1, date2) => {\r\n  if (!date1 || !date2) return false;\r\n  const d1 = new Date(date1);\r\n  const d2 = new Date(date2);\r\n  return d1.toDateString() === d2.toDateString();\r\n};\r\n\r\nconst isToday = (date) => {\r\n  if (!date) return false;\r\n  const today = new Date();\r\n  const checkDate = new Date(date);\r\n  return checkDate.toDateString() === today.toDateString();\r\n};\r\n\r\n// Context for sharing activity data with child components\r\nexport const activityContext = createContext();\r\n\r\nconst InfoLog = styled(Box)(() => ({\r\n  display: \"flex\",\r\n  justifyContent: \"space-between\",\r\n}));\r\n\r\nexport default function Activity() {\r\n  const theme = useTheme();\r\n  const dispatch = useDispatch();\r\n  const profile = useSelector(UserSelector.profile());\r\n  const attendances = useSelector(AttendanceSelector.getAttendances());\r\n  const activities = useSelector(ActivitySelector.getActivityHistory());\r\n  const countLeave = useSelector(LeaveSelector.countLeave());\r\n  const setting = useSelector(SettingSelector.getSetting());\r\n  \r\n  // State variables\r\n  const [controller, setController] = useState(false);\r\n  const [lunchController, setLunch] = useState(false);\r\n  const [lunchType, setLunchType] = useState(localStorage.getItem(\"lunchType\") || \"\");\r\n  const [showBreakPop, setShowBreakPop] = useState(false);\r\n  const [lateCheckIn, setLateCheckIn] = useState(false);\r\n  const [earlyCheckOut, setEarlyCheckOut] = useState(false);\r\n  const [todayStatus, setTodayStatus] = useState(false);\r\n  const [overLimitBreak, setOverLimitBreak] = useState(false);\r\n  const [slotController, setSlotController] = useState(true);\r\n  const [todayActivity, setActivity] = useState([]);\r\n  const [attendance, setAttendance] = useState({});\r\n  const [showGoalPopup, setShowGoalPopup] = useState(false);\r\n  const [workHoursStatus, setWorkHoursStatus] = useState(null);\r\n  const [showWorkHoursStatus, setShowWorkHoursStatus] = useState(false);\r\n  const [checkingIn, setCheckingIn] = useState(false);\r\n  const [checkingOut, setCheckingOut] = useState(false);\r\n\r\n  // Handle break actions based on lunch type selection\r\n  useEffect(() => {\r\n    if (todayActivity.length > 0 && todayActivity[0]?._id && profile && profile._id && attendance && attendance._id) {\r\n      switch (lunchType) {\r\n        case \"lunchBreak\":\r\n          setLunch(!lunchController);\r\n          dispatch(\r\n            AttendanceActions.createLunchBreak({\r\n              id: attendance._id,\r\n              lunchIn: new Date(),\r\n            })\r\n          );\r\n          dispatch(\r\n            ActivityActions.breakStartRed({\r\n              _id: todayActivity[0]._id,\r\n              type: lunchType,\r\n              breakStart: new Date().setMilliseconds(0),\r\n              description: \"Lunch Break\",\r\n              user: profile._id,\r\n            })\r\n          );\r\n          setSlotController(false);\r\n          break;\r\n\r\n        case \"teaBreak\":\r\n          setLunch(!lunchController);\r\n          dispatch(\r\n            AttendanceActions.createLunchBreak({\r\n              id: attendance._id,\r\n              lunchIn: new Date(),\r\n            })\r\n          );\r\n          dispatch(\r\n            ActivityActions.breakStartRed({\r\n              _id: todayActivity[0]._id,\r\n              breakStart: new Date().setMilliseconds(0),\r\n              type: lunchType,\r\n              description: \"Tea Break\",\r\n              user: profile._id,\r\n            })\r\n          );\r\n          setSlotController(false);\r\n          break;\r\n\r\n        case \"other\":\r\n          setLunch(!lunchController);\r\n          dispatch(\r\n            AttendanceActions.createLunchBreak({\r\n              id: attendance._id,\r\n              lunchIn: new Date().setMilliseconds(0),\r\n              user: profile._id,\r\n            })\r\n          );\r\n          setShowBreakPop(true);\r\n          setSlotController(false);\r\n          break;\r\n\r\n        case \"breakOut\":\r\n          setLunch(!lunchController);\r\n          dispatch(\r\n            AttendanceActions.updateLunchBreak({\r\n              id: attendance._id,\r\n              lunchOut: new Date(),\r\n            })\r\n          );\r\n          dispatch(\r\n            ActivityActions.breakEndRed({\r\n              _id: todayActivity[0]._id,\r\n              breakEnd: new Date().setMilliseconds(0),\r\n              type: lunchType,\r\n              user: profile._id,\r\n            })\r\n          );\r\n          setSlotController(true);\r\n          break;\r\n\r\n        default:\r\n          break;\r\n      }\r\n    }\r\n  }, [lunchType]);\r\n\r\n  // Process activity data to find today's activity\r\n  useEffect(() => {\r\n    if (activities && activities.length > 0) {\r\n      console.log('Processing activities to find today\\'s activity:', activities.length);\r\n\r\n      // Find today's activity using the utility function\r\n      const todayActivity = activities.find(activity => {\r\n        if (!activity.checkInTime) {\r\n          console.log('Activity has no checkInTime:', activity._id);\r\n          return false;\r\n        }\r\n\r\n        const isActivityToday = isToday(activity.checkInTime);\r\n        console.log(`Activity ${activity._id}: checkInTime=${activity.checkInTime}, isToday=${isActivityToday}`);\r\n        return isActivityToday;\r\n      });\r\n\r\n      console.log(`Found today's activity:`, todayActivity ? 'Yes' : 'No', todayActivity?._id);\r\n\r\n      if (todayActivity) {\r\n        setActivity([todayActivity]);\r\n        setShowGoalPopup(false);\r\n        sessionStorage.setItem('hasShownGoalPopup', 'true');\r\n      } else {\r\n        // No activity found for today\r\n        setActivity([]);\r\n      }\r\n    }\r\n  }, [activities]);\r\n\r\n  // Handle popup displays for various activity statuses\r\n  useEffect(() => {\r\n    if (todayActivity.length > 0) {\r\n      const hasShownLateCheckInPopup = sessionStorage.getItem('hasShownLateCheckInPopup');\r\n      const hasShownEarlyCheckOutPopup = sessionStorage.getItem('hasShownEarlyCheckOutPopup');\r\n      const hasShownTodayStatusPopup = sessionStorage.getItem('hasShownTodayStatusPopup');\r\n      const hasShownOverLimitBreakPopup = sessionStorage.getItem('hasShownOverLimitBreakPopup');\r\n\r\n      // Handle late check-in popup\r\n      if (\r\n        todayActivity[0].lateCheckInStatus &&\r\n        !todayActivity.some((obj) =>\r\n          Object.prototype.hasOwnProperty.call(obj, \"lateCheckInDiscription\")\r\n        ) &&\r\n        !hasShownLateCheckInPopup\r\n      ) {\r\n        setLateCheckIn(true);\r\n        sessionStorage.setItem('hasShownLateCheckInPopup', 'true');\r\n      }\r\n\r\n      // Handle early check-out popup\r\n      if (\r\n        todayActivity[0].earlyCheckOutStatus &&\r\n        !todayActivity.some((obj) =>\r\n          Object.prototype.hasOwnProperty.call(obj, \"earlyCheckOutDiscription\")\r\n        ) &&\r\n        !hasShownEarlyCheckOutPopup\r\n      ) {\r\n        setEarlyCheckOut(true);\r\n        sessionStorage.setItem('hasShownEarlyCheckOutPopup', 'true');\r\n      }\r\n\r\n      // Handle today's status popup (after checkout)\r\n      if (\r\n        todayActivity.some((obj) =>\r\n          Object.prototype.hasOwnProperty.call(obj, \"checkOutTime\")\r\n        ) &&\r\n        !todayActivity.some((obj) =>\r\n          Object.prototype.hasOwnProperty.call(obj, \"workStatus\")\r\n        ) &&\r\n        !hasShownTodayStatusPopup\r\n      ) {\r\n        setTodayStatus(true);\r\n        sessionStorage.setItem('hasShownTodayStatusPopup', 'true');\r\n      }\r\n\r\n      // Handle over-limit break popup\r\n      if (\r\n        todayActivity[0].breaksHistory &&\r\n        todayActivity[0].overLimitBreakStatus === true &&\r\n        !hasShownOverLimitBreakPopup\r\n      ) {\r\n        setOverLimitBreak(true);\r\n        sessionStorage.setItem('hasShownOverLimitBreakPopup', 'true');\r\n      }\r\n    }\r\n  }, [todayActivity]);\r\n\r\n  // Fetch initial attendance and activity data\r\n  useEffect(() => {\r\n    if (Can(actions.read, features.attendance) && profile && profile._id) {\r\n      // Get the current date in local timezone for consistent date handling\r\n      const today = new Date();\r\n      const formattedDate = today.toISOString().split('T')[0]; // This gives YYYY-MM-DD in UTC\r\n\r\n      // For better timezone handling, let's use local date\r\n      const localYear = today.getFullYear();\r\n      const localMonth = String(today.getMonth() + 1).padStart(2, '0');\r\n      const localDay = String(today.getDate()).padStart(2, '0');\r\n      const localFormattedDate = `${localYear}-${localMonth}-${localDay}`;\r\n\r\n      console.log(`Fetching data for date: ${localFormattedDate} (local timezone)`);\r\n\r\n      // Clear session storage flags at the start of a new day\r\n      const lastLoginDate = localStorage.getItem('lastLoginDate');\r\n      if (lastLoginDate !== localFormattedDate) {\r\n        sessionStorage.removeItem('hasShownGoalPopup');\r\n        sessionStorage.removeItem('hasShownLateCheckInPopup');\r\n        sessionStorage.removeItem('hasShownEarlyCheckOutPopup');\r\n        sessionStorage.removeItem('hasShownTodayStatusPopup');\r\n        sessionStorage.removeItem('hasShownOverLimitBreakPopup');\r\n        localStorage.setItem('lastLoginDate', localFormattedDate);\r\n      }\r\n\r\n      dispatch(\r\n        AttendanceActions.getAttendances({\r\n          user: profile._id,\r\n          date: localFormattedDate, // Use local date for consistency\r\n        })\r\n      );\r\n      dispatch(\r\n        ActivityActions.getUserActivity({\r\n          id: profile._id,\r\n        })\r\n      );\r\n    }\r\n  }, [profile, dispatch]);\r\n\r\n  // Update local attendance state when attendance data changes\r\n  useEffect(() => {\r\n    // Check if the attendance data is for today using local timezone\r\n    const today = new Date();\r\n    const todayDateString = today.toDateString(); // Local timezone date string\r\n\r\n    console.log(`Looking for attendance on: ${todayDateString}`);\r\n\r\n    if (attendances.length > 0) {\r\n      // Filter for today's attendance only using local timezone comparison\r\n      const todayAttendances = attendances.filter(att => {\r\n        if (!att.checkIn) return false;\r\n\r\n        const attDate = new Date(att.checkIn);\r\n        const attDateString = attDate.toDateString(); // Convert UTC to local timezone\r\n\r\n        console.log(`Attendance date: ${attDateString}, matches today: ${attDateString === todayDateString}`);\r\n        return attDateString === todayDateString;\r\n      });\r\n\r\n      console.log(`Found ${todayAttendances.length} attendance records for today`);\r\n\r\n      if (todayAttendances.length > 0) {\r\n        setAttendance(todayAttendances[0]);\r\n      } else {\r\n        // No attendance for today\r\n        setAttendance({});\r\n      }\r\n    } else {\r\n      setAttendance({});\r\n    }\r\n  }, [attendances]);\r\n\r\n  // Refresh attendance data after check-in or break actions\r\n  useEffect(() => {\r\n    if (Can(actions.read, features.attendance) && profile && profile._id) {\r\n      // Get local date for consistent timezone handling\r\n      const today = new Date();\r\n      const localYear = today.getFullYear();\r\n      const localMonth = String(today.getMonth() + 1).padStart(2, '0');\r\n      const localDay = String(today.getDate()).padStart(2, '0');\r\n      const localFormattedDate = `${localYear}-${localMonth}-${localDay}`;\r\n\r\n      // Force cache refresh by adding a timestamp\r\n      const timestamp = Date.now();\r\n\r\n      console.log(`Refreshing attendance data for date: ${localFormattedDate}`);\r\n\r\n      dispatch(\r\n        AttendanceActions.getAttendances({\r\n          user: profile._id,\r\n          date: localFormattedDate, // Use local date\r\n          _t: timestamp, // Add timestamp to prevent caching\r\n        })\r\n      );\r\n    }\r\n    dispatch(\r\n      GeneralActions.removeSuccess(AttendanceActions.createAttendance.type)\r\n    );\r\n  }, [controller, lunchController, profile, dispatch]);\r\n\r\n  // Persist lunch type to localStorage\r\n  useEffect(() => {\r\n    localStorage.setItem(\"lunchType\", lunchType);\r\n  }, [lunchType]);\r\n\r\n  // Handle user check-in action\r\n  const handleCheckIn = () => {\r\n  if (checkingIn) {\r\n    return;\r\n  }\r\n\r\n  console.log(\"check in clicked\");\r\n\r\n  // Clear any existing attendance data to ensure fresh check-in\r\n  setAttendance({});\r\n\r\n  // Reset session storage for a new check-in using local date\r\n  const today = new Date();\r\n  const localYear = today.getFullYear();\r\n  const localMonth = String(today.getMonth() + 1).padStart(2, '0');\r\n  const localDay = String(today.getDate()).padStart(2, '0');\r\n  const localFormattedDate = `${localYear}-${localMonth}-${localDay}`;\r\n  const lastCheckInDate = localStorage.getItem('lastCheckInDate');\r\n\r\n  console.log(`Check-in for date: ${localFormattedDate}`);\r\n\r\n  // If it's a new day, clear all session storage flags\r\n  if (lastCheckInDate !== localFormattedDate) {\r\n    sessionStorage.removeItem('hasShownGoalPopup');\r\n    sessionStorage.removeItem('hasShownLateCheckInPopup');\r\n    sessionStorage.removeItem('hasShownEarlyCheckOutPopup');\r\n    sessionStorage.removeItem('hasShownTodayStatusPopup');\r\n    sessionStorage.removeItem('hasShownOverLimitBreakPopup');\r\n    localStorage.setItem('lastCheckInDate', localFormattedDate);\r\n  }\r\n\r\n  // Show goal popup - the actual check-in will happen after goal submission\r\n  setShowGoalPopup(true);\r\n};\r\n\r\n  const handleSubmitGoal = (goalData) => {\r\n  console.log(\"Goal submitted:\", goalData);\r\n\r\n  // Set checking in state\r\n  setCheckingIn(true);\r\n\r\n  // Close the goal popup\r\n  setShowGoalPopup(false);\r\n\r\n  // Mark as shown to prevent re-showing\r\n  sessionStorage.setItem('hasShownGoalPopup', 'true');\r\n\r\n  // Check if profile exists\r\n  if (!profile || !profile._id) {\r\n    console.error(\"Profile data is missing\");\r\n    setCheckingIn(false);\r\n    return;\r\n  }\r\n\r\n  // Create the goal first\r\n  if (goalData.todaysGoal) {\r\n    dispatch(ActivityActions.createTodayGoal({\r\n      id: profile._id,\r\n      todaysGoal: goalData.todaysGoal\r\n    }));\r\n  }\r\n\r\n  // Then perform check-in with current timestamp\r\n  const checkInTime = new Date();\r\n  console.log(`Check-in time: ${checkInTime.toISOString()} (UTC), Local: ${checkInTime.toString()}`);\r\n\r\n  dispatch(\r\n    AttendanceActions.createAttendance({\r\n      user: profile._id,\r\n      checkIn: checkInTime,\r\n    })\r\n  );\r\n\r\n  // Force immediate refresh of attendance data with local date\r\n  const today = new Date();\r\n  const localYear = today.getFullYear();\r\n  const localMonth = String(today.getMonth() + 1).padStart(2, '0');\r\n  const localDay = String(today.getDate()).padStart(2, '0');\r\n  const localFormattedDate = `${localYear}-${localMonth}-${localDay}`;\r\n  const timestamp = Date.now();\r\n\r\n  // Store the check-in date in localStorage using local date\r\n  localStorage.setItem('lastCheckInDate', localFormattedDate);\r\n\r\n  console.log(`Refreshing attendance data for local date: ${localFormattedDate}`);\r\n\r\n  // Add a small delay before fetching updated attendance data\r\n  setTimeout(() => {\r\n    dispatch(\r\n      AttendanceActions.getAttendances({\r\n        user: profile._id,\r\n        date: localFormattedDate, // Use local date\r\n        _t: timestamp, // Add timestamp to prevent caching\r\n      })\r\n    );\r\n\r\n    // Update controller state\r\n    setController(!controller);\r\n\r\n    // Reset checking state\r\n    setCheckingIn(false);\r\n  }, 1000);\r\n};\r\n  // Handle user check-out action\r\n  const handleCheckOut = () => {\r\n    if (checkingOut) { return; }\r\n    \r\n    // Check if profile and attendance data exist\r\n    if (!profile || !profile._id || !attendance || !attendance._id || !todayActivity || !todayActivity.length) {\r\n      console.error(\"Missing required data for check-out\");\r\n      return;\r\n    }\r\n    \r\n    setCheckingOut(true);\r\n    setController(!controller);\r\n    \r\n    dispatch(\r\n      AttendanceActions.updateAttendance({\r\n        id: attendance._id,\r\n        checkOut: new Date(),\r\n      })\r\n    );\r\n\r\n    dispatch(\r\n      ActivityActions.checkOutStatusUpdate({\r\n        _id: todayActivity[0]._id,\r\n        user: profile._id,\r\n      })\r\n    );\r\n\r\n    // Clear session storage flags when checking out\r\n    sessionStorage.removeItem('hasShownGoalPopup');\r\n    sessionStorage.removeItem('hasShownLateCheckInPopup');\r\n    sessionStorage.removeItem('hasShownEarlyCheckOutPopup');\r\n    sessionStorage.removeItem('hasShownTodayStatusPopup');\r\n    sessionStorage.removeItem('hasShownOverLimitBreakPopup');\r\n\r\n    setTimeout(() => {\r\n      setCheckingOut(false);\r\n    }, 2000);\r\n  };\r\n\r\n  // Listen for checkout success to show work hours status\r\n  const checkoutSuccess = useSelector(state =>\r\n    state.general.success.find(s => s.action === ActivityActions.checkOutStatusUpdate.type)\r\n  );\r\n\r\n  // Update work hours status when checkout is successful\r\n  useEffect(() => {\r\n    if (checkoutSuccess && checkoutSuccess.data) {\r\n      setWorkHoursStatus(checkoutSuccess.data);\r\n      setShowWorkHoursStatus(true);\r\n      setTimeout(() => {\r\n        dispatch(GeneralActions.removeSuccess(ActivityActions.checkOutStatusUpdate.type));\r\n      }, 500);\r\n    }\r\n  }, [checkoutSuccess, dispatch]);\r\n\r\n  // Handle break type selection\r\n  const handleBreakOut = (event) => {\r\n    setLunchType(event.target.value);\r\n  };\r\n\r\n  // Control the visibility of the break reason popup\r\n  function breakPopUpController(val) {\r\n    setShowBreakPop(val);\r\n  }\r\n\r\n  // Check if user has permission to view attendance\r\n  if (!Can(actions.readSelf, features.attendance)) {\r\n    return <div></div>;\r\n  }\r\n\r\n  // UI\r\n  return (\r\n    <Grid container spacing={3} sx={{ padding: \"20px\", display: \"flex\", alignItems: \"center\" }}>\r\n      {/* Productivity Chart */}\r\n      <Grid item xs={12} md={8}>\r\n        <Card sx={{ height: \"100%\", padding: \"20px\" }}>\r\n          <ProductivityChart todayActivities={todayActivity} />\r\n        </Card>\r\n      </Grid>\r\n\r\n      {/* Activity Card */}\r\n      <Grid item xs={12} md={4}>\r\n        <Card\r\n          sx={{\r\n            height: \"100%\",\r\n            display: \"flex\",\r\n            flexDirection: \"column\",\r\n            alignItems: \"center\",\r\n          }}\r\n        >\r\n          <Timer color=\"primary\" sx={{ fontSize: 100 }} />\r\n\r\n          <Box>\r\n            <Typography variant=\"h5\" align=\"center\" gutterBottom sx={{ padding: \"10px\", margin: \"10px\" }}>\r\n              Your Activity Today\r\n            </Typography>\r\n\r\n            <InfoLog>\r\n              {[\"Check In\", \"Check Out\"].map((label, index) => (\r\n                <Box key={label} sx={{ textAlign: \"center\" }}>\r\n                  <Typography>{label}</Typography>\r\n                  <Typography variant=\"subtitle2\">\r\n                    {attendance[index ? \"checkOut\" : \"checkIn\"] ? moment(attendance[index ? \"checkOut\" : \"checkIn\"]).format(\"HH:mm\") : \"-\"}\r\n                  </Typography>\r\n                </Box>\r\n              ))}\r\n            </InfoLog>\r\n          </Box>\r\n\r\n          {!attendance.checkOut && (\r\n            <Box sx={{ mt: 3 }}>\r\n              {/* Check In button */}\r\n              {!attendance.checkIn ? (\r\n                <Button\r\n                  fullWidth\r\n                  variant=\"contained\"\r\n                  onClick={handleCheckIn}\r\n                  disabled={checkingIn}\r\n                  sx={{\r\n                    backgroundColor: checkingIn ? \"#a5d6a7\" : \"green\",\r\n                    color: \"white\",\r\n                    fontWeight: \"bold\",\r\n                    borderRadius: \"8px\",\r\n                    width: \"150px\",\r\n                    height: \"50px\",\r\n                    boxShadow: \"0px 4px 6px rgba(0, 0, 0, 0.2)\",\r\n                    display: \"flex\",\r\n                    alignItems: \"center\",\r\n                    \"&:hover\": { backgroundColor: checkingIn ? \"#a5d6a7\" : \"darkgreen\" },\r\n                  }}\r\n                >\r\n                  {checkingIn ? \"Processing...\" : \"Check In\"}\r\n                </Button>\r\n              ) : (\r\n                <div\r\n                  style={{\r\n                    display: \"flex\",\r\n                    justifyContent: \"space-between\",\r\n                    alignItems: \"center\",\r\n                    width: \"100%\",\r\n                    marginTop: \"10px\",\r\n                    marginBottom: \"10px\",\r\n                    gap: \"10px\",\r\n                  }}\r\n                >\r\n                  {/* Break controls */}\r\n                  {todayActivity.length > 0 && todayActivity[0]?.breakStatus ? (\r\n                    <FormControl>\r\n                      <Select\r\n                        labelId=\"break-label\"\r\n                        value={lunchType}\r\n                        label=\"Break Type\"\r\n                        onChange={handleBreakOut}\r\n                        sx={{\r\n                          backgroundColor: \"#2e7d32\",\r\n                          color: \"white\",\r\n                          fontWeight: \"bold\",\r\n                          borderRadius: \"8px\",\r\n                          width: \"150px\",\r\n                          height: \"50px\",\r\n                          boxShadow: \"0px 4px 6px rgba(0, 0, 0, 0.2)\",\r\n                          display: \"flex\",\r\n                          alignItems: \"center\",\r\n                        }}\r\n                        renderValue={() => \"----\"}\r\n                      >\r\n                        <MenuItem value=\"breakOut\">Break Out</MenuItem>\r\n                      </Select>\r\n                    </FormControl>\r\n                  ) : (\r\n                    <FormControl>\r\n                      <Select\r\n                        labelId=\"break-label\"\r\n                        value={lunchType || \"\"}\r\n                        label=\"Break Type\"\r\n                        onChange={handleBreakOut}\r\n                        sx={{\r\n                          backgroundColor: \"#2e7d32\",\r\n                          color: \"white\",\r\n                          fontWeight: \"bold\",\r\n                          borderRadius: \"8px\",\r\n                          width: \"150px\",\r\n                          height: \"50px\",\r\n                          boxShadow: \"0px 4px 6px rgba(0, 0, 0, 0.2)\",\r\n                          display: \"flex\",\r\n                          alignItems: \"center\",\r\n                        }}\r\n                        renderValue={() => \"Break In\"}\r\n                      >\r\n                        <MenuItem value=\"\" disabled>Break In</MenuItem>\r\n                        <MenuItem value=\"lunchBreak\">Lunch Break</MenuItem>\r\n                        <MenuItem value=\"teaBreak\">Tea Break</MenuItem>\r\n                        <MenuItem value=\"other\">Other</MenuItem>\r\n                      </Select>\r\n                    </FormControl>\r\n                  )}\r\n\r\n                  {/* Check Out button */}\r\n                  <Button\r\n                    variant=\"contained\"\r\n                    color=\"error\"\r\n                    onClick={handleCheckOut}\r\n                    disabled={checkingOut}\r\n                    sx={{\r\n                      backgroundColor: checkingOut ? \"#ef9a9a\" : \"darked\",\r\n                      color: \"white\",\r\n                      fontWeight: \"bold\",\r\n                      borderRadius: \"8px\",\r\n                      width: \"150px\",\r\n                      height: \"50px\",\r\n                      boxShadow: \"0px 4px 6px rgba(0, 0, 0, 0.2)\",\r\n                      display: \"flex\",\r\n                      alignItems: \"center\",\r\n                    }}\r\n                  >\r\n                    {checkingOut ? \"Processing...\" : \"Check Out\"}\r\n                  </Button>\r\n                </div>\r\n              )}\r\n            </Box>\r\n          )}\r\n\r\n          {/* Popups */}\r\n          {showGoalPopup && todayActivity.length === 0 && !sessionStorage.getItem('hasShownGoalPopup') && (\r\n  <TodayGoal\r\n    title=\"Today's Goal\"\r\n    task=\"goal\"\r\n    onSubmit={handleSubmitGoal}  // Fixed typo\r\n    onClose={() => {\r\n      setShowGoalPopup(false);\r\n      sessionStorage.setItem('hasShownGoalPopup', 'true');\r\n    }}\r\n    required={true}  // Make goal mandatory\r\n  />\r\n)}\r\n           \r\n\r\n          {showBreakPop && (\r\n            <OtherBreak\r\n              openVal={true}\r\n              settingFun={breakPopUpController}\r\n              _id={todayActivity[0]._id}\r\n              profile={profile}\r\n            />\r\n          )}\r\n\r\n          {lateCheckIn &&  (\r\n            <EarlyLate\r\n              openVal={true}\r\n              task=\"late\"\r\n              dialogTitle=\"Late Check In\"\r\n              id={todayActivity[0]._id}\r\n              onClose={() => {\r\n                sessionStorage.setItem('hasShownLateCheckInPopup', 'true');\r\n                setLateCheckIn(false);\r\n              }}\r\n            />\r\n          )}\r\n\r\n          {todayStatus &&  (\r\n            <EarlyLate\r\n              openVal={true}\r\n              task=\"status\"\r\n              dialogTitle=\"Work Status\"\r\n              id={todayActivity[0]._id}\r\n              onClose={() => {\r\n                sessionStorage.setItem('hasShownTodayStatusPopup', 'true');\r\n                setTodayStatus(false);\r\n              }}\r\n            />\r\n          )}\r\n\r\n          {earlyCheckOut &&  (\r\n            <EarlyLate\r\n              openVal={true}\r\n              task=\"early\"\r\n              dialogTitle=\"Early Check Out\"\r\n              id={todayActivity[0]._id}\r\n              onClose={() => {\r\n                sessionStorage.setItem('hasShownEarlyCheckOutPopup', 'true');\r\n                setEarlyCheckOut(false);\r\n              }}\r\n            />\r\n          )}\r\n\r\n          {overLimitBreak &&  (\r\n            <OverLimitBreak\r\n              openVal={true}\r\n              id={todayActivity[0]._id}\r\n              onClose={() => {\r\n                sessionStorage.setItem('hasShownOverLimitBreakPopup', 'true');\r\n                setOverLimitBreak(false);\r\n              }}\r\n            />\r\n          )}\r\n\r\n          {/* Work Hours Status Dialog */}\r\n          <WorkHoursStatus\r\n            open={showWorkHoursStatus}\r\n            statusData={workHoursStatus}\r\n            onClose={() => setShowWorkHoursStatus(false)}\r\n          />\r\n        </Card>\r\n      </Grid>\r\n\r\n      {/* Bottom Section */}\r\n      <Grid item xs={12}>\r\n        <Grid container spacing={3} sx={{ display: \"flex\", alignItems: \"center\" }}>\r\n          {/* Attendance Bar Chart */}\r\n          <Grid item xs={12} md={6}>\r\n            <AttendanceBarChart activities={activities} />\r\n          </Grid>\r\n\r\n          {/* Leave Information */}\r\n          {Can(actions.readSelf, features.leave) && (\r\n            <Grid item xs={12} md={6} container spacing={2}>\r\n              <Grid item xs={12}>\r\n                <Widget\r\n                  title=\"Leave Taken\"\r\n                  content={countLeave ?? 0}\r\n                  icon={<AssignmentIndOutlined sx={{ color: theme.palette.primary.main, fontSize: 62 }} />}\r\n                />\r\n              </Grid>\r\n              {Can(actions.read, features.attendance) && (\r\n                <Grid item xs={12}>\r\n                  <Widget\r\n                    title=\"Quote Leave\"\r\n                    content={setting?.leaveLimit ?? 0}\r\n                    icon={<AssignmentIndOutlined sx={{ color: theme.palette.primary.main, fontSize: 62 }} />}\r\n                  />\r\n                </Grid>\r\n              )}\r\n            </Grid>\r\n          )}\r\n        </Grid>\r\n      </Grid>\r\n    </Grid>\r\n  );\r\n\r\n}"], "mappings": ";;AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,aAAa,QAAQ,OAAO;AACjE,OAAOC,GAAG,MAAM,oBAAoB;AACpC,SAASC,OAAO,EAAEC,QAAQ,QAAQ,+BAA+B;AACjE,SACEC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,UAAU,EACVC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,gBAAgB,QACX,eAAe;AACtB,SAASC,KAAK,EAAEC,qBAAqB,QAAQ,qBAAqB;AAClE,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SACEC,eAAe,EACfC,iBAAiB,EACjBC,cAAc,QACT,yBAAyB;AAChC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,kBAAkB,EAAEC,YAAY,QAAQ,oBAAoB;AACrE,SAASC,gBAAgB,QAAQ,4BAA4B;AAC7D,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,MAAM,EAAEC,QAAQ,QAAQ,sBAAsB;AACvD,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,UAAU,MAAM,gBAAgB;AACvC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,MAAM,MAAM,UAAU;;AAE7B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,kBAAkB,GAAGA,CAACC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC,KAAK;EAChD,MAAMC,IAAI,GAAGF,IAAI,CAACG,WAAW,CAAC,CAAC;EAC/B,MAAMC,KAAK,GAAGC,MAAM,CAACL,IAAI,CAACM,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EAC1D,MAAMC,GAAG,GAAGH,MAAM,CAACL,IAAI,CAACS,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EACnD,OAAO,GAAGL,IAAI,IAAIE,KAAK,IAAII,GAAG,EAAE;AAClC,CAAC;AAED,MAAME,eAAe,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;EACxC,IAAI,CAACD,KAAK,IAAI,CAACC,KAAK,EAAE,OAAO,KAAK;EAClC,MAAMC,EAAE,GAAG,IAAIZ,IAAI,CAACU,KAAK,CAAC;EAC1B,MAAMG,EAAE,GAAG,IAAIb,IAAI,CAACW,KAAK,CAAC;EAC1B,OAAOC,EAAE,CAACE,YAAY,CAAC,CAAC,KAAKD,EAAE,CAACC,YAAY,CAAC,CAAC;AAChD,CAAC;AAED,MAAMC,OAAO,GAAIhB,IAAI,IAAK;EACxB,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK;EACvB,MAAMiB,KAAK,GAAG,IAAIhB,IAAI,CAAC,CAAC;EACxB,MAAMiB,SAAS,GAAG,IAAIjB,IAAI,CAACD,IAAI,CAAC;EAChC,OAAOkB,SAAS,CAACH,YAAY,CAAC,CAAC,KAAKE,KAAK,CAACF,YAAY,CAAC,CAAC;AAC1D,CAAC;;AAED;AACA,OAAO,MAAMI,eAAe,gBAAG1D,aAAa,CAAC,CAAC;AAE9C,MAAM2D,OAAO,GAAGjC,MAAM,CAACtB,GAAG,CAAC,CAAC,OAAO;EACjCwD,OAAO,EAAE,MAAM;EACfC,cAAc,EAAE;AAClB,CAAC,CAAC,CAAC;AAACC,EAAA,GAHEH,OAAO;AAKb,eAAe,SAASI,QAAQA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,gBAAA,EAAAC,mBAAA;EACjC,MAAMC,KAAK,GAAGxC,QAAQ,CAAC,CAAC;EACxB,MAAMyC,QAAQ,GAAGjD,WAAW,CAAC,CAAC;EAC9B,MAAMkD,OAAO,GAAGjD,WAAW,CAACE,YAAY,CAAC+C,OAAO,CAAC,CAAC,CAAC;EACnD,MAAMC,WAAW,GAAGlD,WAAW,CAACC,kBAAkB,CAACkD,cAAc,CAAC,CAAC,CAAC;EACpE,MAAMC,UAAU,GAAGpD,WAAW,CAACG,gBAAgB,CAACkD,kBAAkB,CAAC,CAAC,CAAC;EACrE,MAAMC,UAAU,GAAGtD,WAAW,CAACI,aAAa,CAACkD,UAAU,CAAC,CAAC,CAAC;EAC1D,MAAMC,OAAO,GAAGvD,WAAW,CAACK,eAAe,CAACmD,UAAU,CAAC,CAAC,CAAC;;EAEzD;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACgF,eAAe,EAAEC,QAAQ,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACkF,SAAS,EAAEC,YAAY,CAAC,GAAGnF,QAAQ,CAACoF,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;EACnF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGvF,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACwF,WAAW,EAAEC,cAAc,CAAC,GAAGzF,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC0F,aAAa,EAAEC,gBAAgB,CAAC,GAAG3F,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4F,WAAW,EAAEC,cAAc,CAAC,GAAG7F,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC8F,cAAc,EAAEC,iBAAiB,CAAC,GAAG/F,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACgG,cAAc,EAAEC,iBAAiB,CAAC,GAAGjG,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACkG,aAAa,EAAEC,WAAW,CAAC,GAAGnG,QAAQ,CAAC,EAAE,CAAC;EACjD,MAAM,CAACoG,UAAU,EAAEC,aAAa,CAAC,GAAGrG,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACsG,aAAa,EAAEC,gBAAgB,CAAC,GAAGvG,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACwG,eAAe,EAAEC,kBAAkB,CAAC,GAAGzG,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC0G,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG3G,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC4G,UAAU,EAAEC,aAAa,CAAC,GAAG7G,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC8G,WAAW,EAAEC,cAAc,CAAC,GAAG/G,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACAD,SAAS,CAAC,MAAM;IAAA,IAAAiH,eAAA;IACd,IAAId,aAAa,CAACe,MAAM,GAAG,CAAC,KAAAD,eAAA,GAAId,aAAa,CAAC,CAAC,CAAC,cAAAc,eAAA,eAAhBA,eAAA,CAAkBE,GAAG,IAAI5C,OAAO,IAAIA,OAAO,CAAC4C,GAAG,IAAId,UAAU,IAAIA,UAAU,CAACc,GAAG,EAAE;MAC/G,QAAQhC,SAAS;QACf,KAAK,YAAY;UACfD,QAAQ,CAAC,CAACD,eAAe,CAAC;UAC1BX,QAAQ,CACNnD,iBAAiB,CAACiG,gBAAgB,CAAC;YACjCC,EAAE,EAAEhB,UAAU,CAACc,GAAG;YAClBG,OAAO,EAAE,IAAI5E,IAAI,CAAC;UACpB,CAAC,CACH,CAAC;UACD4B,QAAQ,CACNpD,eAAe,CAACqG,aAAa,CAAC;YAC5BJ,GAAG,EAAEhB,aAAa,CAAC,CAAC,CAAC,CAACgB,GAAG;YACzBK,IAAI,EAAErC,SAAS;YACfsC,UAAU,EAAE,IAAI/E,IAAI,CAAC,CAAC,CAACgF,eAAe,CAAC,CAAC,CAAC;YACzCC,WAAW,EAAE,aAAa;YAC1BC,IAAI,EAAErD,OAAO,CAAC4C;UAChB,CAAC,CACH,CAAC;UACDjB,iBAAiB,CAAC,KAAK,CAAC;UACxB;QAEF,KAAK,UAAU;UACbhB,QAAQ,CAAC,CAACD,eAAe,CAAC;UAC1BX,QAAQ,CACNnD,iBAAiB,CAACiG,gBAAgB,CAAC;YACjCC,EAAE,EAAEhB,UAAU,CAACc,GAAG;YAClBG,OAAO,EAAE,IAAI5E,IAAI,CAAC;UACpB,CAAC,CACH,CAAC;UACD4B,QAAQ,CACNpD,eAAe,CAACqG,aAAa,CAAC;YAC5BJ,GAAG,EAAEhB,aAAa,CAAC,CAAC,CAAC,CAACgB,GAAG;YACzBM,UAAU,EAAE,IAAI/E,IAAI,CAAC,CAAC,CAACgF,eAAe,CAAC,CAAC,CAAC;YACzCF,IAAI,EAAErC,SAAS;YACfwC,WAAW,EAAE,WAAW;YACxBC,IAAI,EAAErD,OAAO,CAAC4C;UAChB,CAAC,CACH,CAAC;UACDjB,iBAAiB,CAAC,KAAK,CAAC;UACxB;QAEF,KAAK,OAAO;UACVhB,QAAQ,CAAC,CAACD,eAAe,CAAC;UAC1BX,QAAQ,CACNnD,iBAAiB,CAACiG,gBAAgB,CAAC;YACjCC,EAAE,EAAEhB,UAAU,CAACc,GAAG;YAClBG,OAAO,EAAE,IAAI5E,IAAI,CAAC,CAAC,CAACgF,eAAe,CAAC,CAAC,CAAC;YACtCE,IAAI,EAAErD,OAAO,CAAC4C;UAChB,CAAC,CACH,CAAC;UACD3B,eAAe,CAAC,IAAI,CAAC;UACrBU,iBAAiB,CAAC,KAAK,CAAC;UACxB;QAEF,KAAK,UAAU;UACbhB,QAAQ,CAAC,CAACD,eAAe,CAAC;UAC1BX,QAAQ,CACNnD,iBAAiB,CAAC0G,gBAAgB,CAAC;YACjCR,EAAE,EAAEhB,UAAU,CAACc,GAAG;YAClBW,QAAQ,EAAE,IAAIpF,IAAI,CAAC;UACrB,CAAC,CACH,CAAC;UACD4B,QAAQ,CACNpD,eAAe,CAAC6G,WAAW,CAAC;YAC1BZ,GAAG,EAAEhB,aAAa,CAAC,CAAC,CAAC,CAACgB,GAAG;YACzBa,QAAQ,EAAE,IAAItF,IAAI,CAAC,CAAC,CAACgF,eAAe,CAAC,CAAC,CAAC;YACvCF,IAAI,EAAErC,SAAS;YACfyC,IAAI,EAAErD,OAAO,CAAC4C;UAChB,CAAC,CACH,CAAC;UACDjB,iBAAiB,CAAC,IAAI,CAAC;UACvB;QAEF;UACE;MACJ;IACF;EACF,CAAC,EAAE,CAACf,SAAS,CAAC,CAAC;;EAEf;EACAnF,SAAS,CAAC,MAAM;IACd,IAAI0E,UAAU,IAAIA,UAAU,CAACwC,MAAM,GAAG,CAAC,EAAE;MACvCe,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAExD,UAAU,CAACwC,MAAM,CAAC;;MAElF;MACA,MAAMf,aAAa,GAAGzB,UAAU,CAACyD,IAAI,CAACC,QAAQ,IAAI;QAChD,IAAI,CAACA,QAAQ,CAACC,WAAW,EAAE;UACzBJ,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEE,QAAQ,CAACjB,GAAG,CAAC;UACzD,OAAO,KAAK;QACd;QAEA,MAAMmB,eAAe,GAAG7E,OAAO,CAAC2E,QAAQ,CAACC,WAAW,CAAC;QACrDJ,OAAO,CAACC,GAAG,CAAC,YAAYE,QAAQ,CAACjB,GAAG,iBAAiBiB,QAAQ,CAACC,WAAW,aAAaC,eAAe,EAAE,CAAC;QACxG,OAAOA,eAAe;MACxB,CAAC,CAAC;MAEFL,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE/B,aAAa,GAAG,KAAK,GAAG,IAAI,EAAEA,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEgB,GAAG,CAAC;MAExF,IAAIhB,aAAa,EAAE;QACjBC,WAAW,CAAC,CAACD,aAAa,CAAC,CAAC;QAC5BK,gBAAgB,CAAC,KAAK,CAAC;QACvB+B,cAAc,CAACC,OAAO,CAAC,mBAAmB,EAAE,MAAM,CAAC;MACrD,CAAC,MAAM;QACL;QACApC,WAAW,CAAC,EAAE,CAAC;MACjB;IACF;EACF,CAAC,EAAE,CAAC1B,UAAU,CAAC,CAAC;;EAEhB;EACA1E,SAAS,CAAC,MAAM;IACd,IAAImG,aAAa,CAACe,MAAM,GAAG,CAAC,EAAE;MAC5B,MAAMuB,wBAAwB,GAAGF,cAAc,CAACjD,OAAO,CAAC,0BAA0B,CAAC;MACnF,MAAMoD,0BAA0B,GAAGH,cAAc,CAACjD,OAAO,CAAC,4BAA4B,CAAC;MACvF,MAAMqD,wBAAwB,GAAGJ,cAAc,CAACjD,OAAO,CAAC,0BAA0B,CAAC;MACnF,MAAMsD,2BAA2B,GAAGL,cAAc,CAACjD,OAAO,CAAC,6BAA6B,CAAC;;MAEzF;MACA,IACEa,aAAa,CAAC,CAAC,CAAC,CAAC0C,iBAAiB,IAClC,CAAC1C,aAAa,CAAC2C,IAAI,CAAEC,GAAG,IACtBC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,GAAG,EAAE,wBAAwB,CACpE,CAAC,IACD,CAACN,wBAAwB,EACzB;QACA/C,cAAc,CAAC,IAAI,CAAC;QACpB6C,cAAc,CAACC,OAAO,CAAC,0BAA0B,EAAE,MAAM,CAAC;MAC5D;;MAEA;MACA,IACErC,aAAa,CAAC,CAAC,CAAC,CAACiD,mBAAmB,IACpC,CAACjD,aAAa,CAAC2C,IAAI,CAAEC,GAAG,IACtBC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,GAAG,EAAE,0BAA0B,CACtE,CAAC,IACD,CAACL,0BAA0B,EAC3B;QACA9C,gBAAgB,CAAC,IAAI,CAAC;QACtB2C,cAAc,CAACC,OAAO,CAAC,4BAA4B,EAAE,MAAM,CAAC;MAC9D;;MAEA;MACA,IACErC,aAAa,CAAC2C,IAAI,CAAEC,GAAG,IACrBC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,GAAG,EAAE,cAAc,CAC1D,CAAC,IACD,CAAC5C,aAAa,CAAC2C,IAAI,CAAEC,GAAG,IACtBC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,GAAG,EAAE,YAAY,CACxD,CAAC,IACD,CAACJ,wBAAwB,EACzB;QACA7C,cAAc,CAAC,IAAI,CAAC;QACpByC,cAAc,CAACC,OAAO,CAAC,0BAA0B,EAAE,MAAM,CAAC;MAC5D;;MAEA;MACA,IACErC,aAAa,CAAC,CAAC,CAAC,CAACkD,aAAa,IAC9BlD,aAAa,CAAC,CAAC,CAAC,CAACmD,oBAAoB,KAAK,IAAI,IAC9C,CAACV,2BAA2B,EAC5B;QACA5C,iBAAiB,CAAC,IAAI,CAAC;QACvBuC,cAAc,CAACC,OAAO,CAAC,6BAA6B,EAAE,MAAM,CAAC;MAC/D;IACF;EACF,CAAC,EAAE,CAACrC,aAAa,CAAC,CAAC;;EAEnB;EACAnG,SAAS,CAAC,MAAM;IACd,IAAIG,GAAG,CAACC,OAAO,CAACmJ,IAAI,EAAElJ,QAAQ,CAACgG,UAAU,CAAC,IAAI9B,OAAO,IAAIA,OAAO,CAAC4C,GAAG,EAAE;MACpE;MACA,MAAMzD,KAAK,GAAG,IAAIhB,IAAI,CAAC,CAAC;MACxB,MAAM8G,aAAa,GAAG9F,KAAK,CAAC+F,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEzD;MACA,MAAMC,SAAS,GAAGjG,KAAK,CAACd,WAAW,CAAC,CAAC;MACrC,MAAMgH,UAAU,GAAG9G,MAAM,CAACY,KAAK,CAACX,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAChE,MAAM6G,QAAQ,GAAG/G,MAAM,CAACY,KAAK,CAACR,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACzD,MAAM8G,kBAAkB,GAAG,GAAGH,SAAS,IAAIC,UAAU,IAAIC,QAAQ,EAAE;MAEnE5B,OAAO,CAACC,GAAG,CAAC,2BAA2B4B,kBAAkB,mBAAmB,CAAC;;MAE7E;MACA,MAAMC,aAAa,GAAG1E,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;MAC3D,IAAIyE,aAAa,KAAKD,kBAAkB,EAAE;QACxCvB,cAAc,CAACyB,UAAU,CAAC,mBAAmB,CAAC;QAC9CzB,cAAc,CAACyB,UAAU,CAAC,0BAA0B,CAAC;QACrDzB,cAAc,CAACyB,UAAU,CAAC,4BAA4B,CAAC;QACvDzB,cAAc,CAACyB,UAAU,CAAC,0BAA0B,CAAC;QACrDzB,cAAc,CAACyB,UAAU,CAAC,6BAA6B,CAAC;QACxD3E,YAAY,CAACmD,OAAO,CAAC,eAAe,EAAEsB,kBAAkB,CAAC;MAC3D;MAEAxF,QAAQ,CACNnD,iBAAiB,CAACsD,cAAc,CAAC;QAC/BmD,IAAI,EAAErD,OAAO,CAAC4C,GAAG;QACjB1E,IAAI,EAAEqH,kBAAkB,CAAE;MAC5B,CAAC,CACH,CAAC;MACDxF,QAAQ,CACNpD,eAAe,CAAC+I,eAAe,CAAC;QAC9B5C,EAAE,EAAE9C,OAAO,CAAC4C;MACd,CAAC,CACH,CAAC;IACH;EACF,CAAC,EAAE,CAAC5C,OAAO,EAAED,QAAQ,CAAC,CAAC;;EAEvB;EACAtE,SAAS,CAAC,MAAM;IACd;IACA,MAAM0D,KAAK,GAAG,IAAIhB,IAAI,CAAC,CAAC;IACxB,MAAMwH,eAAe,GAAGxG,KAAK,CAACF,YAAY,CAAC,CAAC,CAAC,CAAC;;IAE9CyE,OAAO,CAACC,GAAG,CAAC,8BAA8BgC,eAAe,EAAE,CAAC;IAE5D,IAAI1F,WAAW,CAAC0C,MAAM,GAAG,CAAC,EAAE;MAC1B;MACA,MAAMiD,gBAAgB,GAAG3F,WAAW,CAAC4F,MAAM,CAACC,GAAG,IAAI;QACjD,IAAI,CAACA,GAAG,CAACC,OAAO,EAAE,OAAO,KAAK;QAE9B,MAAMC,OAAO,GAAG,IAAI7H,IAAI,CAAC2H,GAAG,CAACC,OAAO,CAAC;QACrC,MAAME,aAAa,GAAGD,OAAO,CAAC/G,YAAY,CAAC,CAAC,CAAC,CAAC;;QAE9CyE,OAAO,CAACC,GAAG,CAAC,oBAAoBsC,aAAa,oBAAoBA,aAAa,KAAKN,eAAe,EAAE,CAAC;QACrG,OAAOM,aAAa,KAAKN,eAAe;MAC1C,CAAC,CAAC;MAEFjC,OAAO,CAACC,GAAG,CAAC,SAASiC,gBAAgB,CAACjD,MAAM,+BAA+B,CAAC;MAE5E,IAAIiD,gBAAgB,CAACjD,MAAM,GAAG,CAAC,EAAE;QAC/BZ,aAAa,CAAC6D,gBAAgB,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,MAAM;QACL;QACA7D,aAAa,CAAC,CAAC,CAAC,CAAC;MACnB;IACF,CAAC,MAAM;MACLA,aAAa,CAAC,CAAC,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAAC9B,WAAW,CAAC,CAAC;;EAEjB;EACAxE,SAAS,CAAC,MAAM;IACd,IAAIG,GAAG,CAACC,OAAO,CAACmJ,IAAI,EAAElJ,QAAQ,CAACgG,UAAU,CAAC,IAAI9B,OAAO,IAAIA,OAAO,CAAC4C,GAAG,EAAE;MACpE;MACA,MAAMzD,KAAK,GAAG,IAAIhB,IAAI,CAAC,CAAC;MACxB,MAAMiH,SAAS,GAAGjG,KAAK,CAACd,WAAW,CAAC,CAAC;MACrC,MAAMgH,UAAU,GAAG9G,MAAM,CAACY,KAAK,CAACX,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAChE,MAAM6G,QAAQ,GAAG/G,MAAM,CAACY,KAAK,CAACR,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACzD,MAAM8G,kBAAkB,GAAG,GAAGH,SAAS,IAAIC,UAAU,IAAIC,QAAQ,EAAE;;MAEnE;MACA,MAAMY,SAAS,GAAG/H,IAAI,CAACgI,GAAG,CAAC,CAAC;MAE5BzC,OAAO,CAACC,GAAG,CAAC,wCAAwC4B,kBAAkB,EAAE,CAAC;MAEzExF,QAAQ,CACNnD,iBAAiB,CAACsD,cAAc,CAAC;QAC/BmD,IAAI,EAAErD,OAAO,CAAC4C,GAAG;QACjB1E,IAAI,EAAEqH,kBAAkB;QAAE;QAC1Ba,EAAE,EAAEF,SAAS,CAAE;MACjB,CAAC,CACH,CAAC;IACH;IACAnG,QAAQ,CACNlD,cAAc,CAACwJ,aAAa,CAACzJ,iBAAiB,CAAC0J,gBAAgB,CAACrD,IAAI,CACtE,CAAC;EACH,CAAC,EAAE,CAACzC,UAAU,EAAEE,eAAe,EAAEV,OAAO,EAAED,QAAQ,CAAC,CAAC;;EAEpD;EACAtE,SAAS,CAAC,MAAM;IACdqF,YAAY,CAACmD,OAAO,CAAC,WAAW,EAAErD,SAAS,CAAC;EAC9C,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;;EAEf;EACA,MAAM2F,aAAa,GAAGA,CAAA,KAAM;IAC5B,IAAIjE,UAAU,EAAE;MACd;IACF;IAEAoB,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;;IAE/B;IACA5B,aAAa,CAAC,CAAC,CAAC,CAAC;;IAEjB;IACA,MAAM5C,KAAK,GAAG,IAAIhB,IAAI,CAAC,CAAC;IACxB,MAAMiH,SAAS,GAAGjG,KAAK,CAACd,WAAW,CAAC,CAAC;IACrC,MAAMgH,UAAU,GAAG9G,MAAM,CAACY,KAAK,CAACX,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAChE,MAAM6G,QAAQ,GAAG/G,MAAM,CAACY,KAAK,CAACR,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACzD,MAAM8G,kBAAkB,GAAG,GAAGH,SAAS,IAAIC,UAAU,IAAIC,QAAQ,EAAE;IACnE,MAAMkB,eAAe,GAAG1F,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC;IAE/D2C,OAAO,CAACC,GAAG,CAAC,sBAAsB4B,kBAAkB,EAAE,CAAC;;IAEvD;IACA,IAAIiB,eAAe,KAAKjB,kBAAkB,EAAE;MAC1CvB,cAAc,CAACyB,UAAU,CAAC,mBAAmB,CAAC;MAC9CzB,cAAc,CAACyB,UAAU,CAAC,0BAA0B,CAAC;MACrDzB,cAAc,CAACyB,UAAU,CAAC,4BAA4B,CAAC;MACvDzB,cAAc,CAACyB,UAAU,CAAC,0BAA0B,CAAC;MACrDzB,cAAc,CAACyB,UAAU,CAAC,6BAA6B,CAAC;MACxD3E,YAAY,CAACmD,OAAO,CAAC,iBAAiB,EAAEsB,kBAAkB,CAAC;IAC7D;;IAEA;IACAtD,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAEC,MAAMwE,gBAAgB,GAAIC,QAAQ,IAAK;IACvChD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE+C,QAAQ,CAAC;;IAExC;IACAnE,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACAN,gBAAgB,CAAC,KAAK,CAAC;;IAEvB;IACA+B,cAAc,CAACC,OAAO,CAAC,mBAAmB,EAAE,MAAM,CAAC;;IAEnD;IACA,IAAI,CAACjE,OAAO,IAAI,CAACA,OAAO,CAAC4C,GAAG,EAAE;MAC5Bc,OAAO,CAACiD,KAAK,CAAC,yBAAyB,CAAC;MACxCpE,aAAa,CAAC,KAAK,CAAC;MACpB;IACF;;IAEA;IACA,IAAImE,QAAQ,CAACE,UAAU,EAAE;MACvB7G,QAAQ,CAACpD,eAAe,CAACkK,eAAe,CAAC;QACvC/D,EAAE,EAAE9C,OAAO,CAAC4C,GAAG;QACfgE,UAAU,EAAEF,QAAQ,CAACE;MACvB,CAAC,CAAC,CAAC;IACL;;IAEA;IACA,MAAM9C,WAAW,GAAG,IAAI3F,IAAI,CAAC,CAAC;IAC9BuF,OAAO,CAACC,GAAG,CAAC,kBAAkBG,WAAW,CAACoB,WAAW,CAAC,CAAC,kBAAkBpB,WAAW,CAACgD,QAAQ,CAAC,CAAC,EAAE,CAAC;IAElG/G,QAAQ,CACNnD,iBAAiB,CAAC0J,gBAAgB,CAAC;MACjCjD,IAAI,EAAErD,OAAO,CAAC4C,GAAG;MACjBmD,OAAO,EAAEjC;IACX,CAAC,CACH,CAAC;;IAED;IACA,MAAM3E,KAAK,GAAG,IAAIhB,IAAI,CAAC,CAAC;IACxB,MAAMiH,SAAS,GAAGjG,KAAK,CAACd,WAAW,CAAC,CAAC;IACrC,MAAMgH,UAAU,GAAG9G,MAAM,CAACY,KAAK,CAACX,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAChE,MAAM6G,QAAQ,GAAG/G,MAAM,CAACY,KAAK,CAACR,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACzD,MAAM8G,kBAAkB,GAAG,GAAGH,SAAS,IAAIC,UAAU,IAAIC,QAAQ,EAAE;IACnE,MAAMY,SAAS,GAAG/H,IAAI,CAACgI,GAAG,CAAC,CAAC;;IAE5B;IACArF,YAAY,CAACmD,OAAO,CAAC,iBAAiB,EAAEsB,kBAAkB,CAAC;IAE3D7B,OAAO,CAACC,GAAG,CAAC,8CAA8C4B,kBAAkB,EAAE,CAAC;;IAE/E;IACAwB,UAAU,CAAC,MAAM;MACfhH,QAAQ,CACNnD,iBAAiB,CAACsD,cAAc,CAAC;QAC/BmD,IAAI,EAAErD,OAAO,CAAC4C,GAAG;QACjB1E,IAAI,EAAEqH,kBAAkB;QAAE;QAC1Ba,EAAE,EAAEF,SAAS,CAAE;MACjB,CAAC,CACH,CAAC;;MAED;MACAzF,aAAa,CAAC,CAACD,UAAU,CAAC;;MAE1B;MACA+B,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACC;EACA,MAAMyE,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIxE,WAAW,EAAE;MAAE;IAAQ;;IAE3B;IACA,IAAI,CAACxC,OAAO,IAAI,CAACA,OAAO,CAAC4C,GAAG,IAAI,CAACd,UAAU,IAAI,CAACA,UAAU,CAACc,GAAG,IAAI,CAAChB,aAAa,IAAI,CAACA,aAAa,CAACe,MAAM,EAAE;MACzGe,OAAO,CAACiD,KAAK,CAAC,qCAAqC,CAAC;MACpD;IACF;IAEAlE,cAAc,CAAC,IAAI,CAAC;IACpBhC,aAAa,CAAC,CAACD,UAAU,CAAC;IAE1BT,QAAQ,CACNnD,iBAAiB,CAACqK,gBAAgB,CAAC;MACjCnE,EAAE,EAAEhB,UAAU,CAACc,GAAG;MAClBsE,QAAQ,EAAE,IAAI/I,IAAI,CAAC;IACrB,CAAC,CACH,CAAC;IAED4B,QAAQ,CACNpD,eAAe,CAACwK,oBAAoB,CAAC;MACnCvE,GAAG,EAAEhB,aAAa,CAAC,CAAC,CAAC,CAACgB,GAAG;MACzBS,IAAI,EAAErD,OAAO,CAAC4C;IAChB,CAAC,CACH,CAAC;;IAED;IACAoB,cAAc,CAACyB,UAAU,CAAC,mBAAmB,CAAC;IAC9CzB,cAAc,CAACyB,UAAU,CAAC,0BAA0B,CAAC;IACrDzB,cAAc,CAACyB,UAAU,CAAC,4BAA4B,CAAC;IACvDzB,cAAc,CAACyB,UAAU,CAAC,0BAA0B,CAAC;IACrDzB,cAAc,CAACyB,UAAU,CAAC,6BAA6B,CAAC;IAExDsB,UAAU,CAAC,MAAM;MACftE,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;;EAED;EACA,MAAM2E,eAAe,GAAGrK,WAAW,CAACsK,KAAK,IACvCA,KAAK,CAACC,OAAO,CAACC,OAAO,CAAC3D,IAAI,CAAC4D,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK9K,eAAe,CAACwK,oBAAoB,CAAClE,IAAI,CACxF,CAAC;;EAED;EACAxH,SAAS,CAAC,MAAM;IACd,IAAI2L,eAAe,IAAIA,eAAe,CAACM,IAAI,EAAE;MAC3CvF,kBAAkB,CAACiF,eAAe,CAACM,IAAI,CAAC;MACxCrF,sBAAsB,CAAC,IAAI,CAAC;MAC5B0E,UAAU,CAAC,MAAM;QACfhH,QAAQ,CAAClD,cAAc,CAACwJ,aAAa,CAAC1J,eAAe,CAACwK,oBAAoB,CAAClE,IAAI,CAAC,CAAC;MACnF,CAAC,EAAE,GAAG,CAAC;IACT;EACF,CAAC,EAAE,CAACmE,eAAe,EAAErH,QAAQ,CAAC,CAAC;;EAE/B;EACA,MAAM4H,cAAc,GAAIC,KAAK,IAAK;IAChC/G,YAAY,CAAC+G,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EAClC,CAAC;;EAED;EACA,SAASC,oBAAoBA,CAACC,GAAG,EAAE;IACjC/G,eAAe,CAAC+G,GAAG,CAAC;EACtB;;EAEA;EACA,IAAI,CAACpM,GAAG,CAACC,OAAO,CAACoM,QAAQ,EAAEnM,QAAQ,CAACgG,UAAU,CAAC,EAAE;IAC/C,oBAAO9D,OAAA;MAAAkK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAU,CAAC;EACpB;;EAEA;EACA,oBACErK,OAAA,CAAC9B,IAAI;IAACoM,SAAS;IAACC,OAAO,EAAE,CAAE;IAACC,EAAE,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAElJ,OAAO,EAAE,MAAM;MAAEmJ,UAAU,EAAE;IAAS,CAAE;IAAAC,QAAA,gBAEzF3K,OAAA,CAAC9B,IAAI;MAAC0M,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACvB3K,OAAA,CAAC/B,IAAI;QAACuM,EAAE,EAAE;UAAEO,MAAM,EAAE,MAAM;UAAEN,OAAO,EAAE;QAAO,CAAE;QAAAE,QAAA,eAC5C3K,OAAA,CAACJ,iBAAiB;UAACoL,eAAe,EAAEpH;QAAc;UAAAsG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPrK,OAAA,CAAC9B,IAAI;MAAC0M,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACvB3K,OAAA,CAAC/B,IAAI;QACHuM,EAAE,EAAE;UACFO,MAAM,EAAE,MAAM;UACdxJ,OAAO,EAAE,MAAM;UACf0J,aAAa,EAAE,QAAQ;UACvBP,UAAU,EAAE;QACd,CAAE;QAAAC,QAAA,gBAEF3K,OAAA,CAACxB,KAAK;UAAC0M,KAAK,EAAC,SAAS;UAACV,EAAE,EAAE;YAAEW,QAAQ,EAAE;UAAI;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEhDrK,OAAA,CAACjC,GAAG;UAAA4M,QAAA,gBACF3K,OAAA,CAAC7B,UAAU;YAACiN,OAAO,EAAC,IAAI;YAACC,KAAK,EAAC,QAAQ;YAACC,YAAY;YAACd,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEc,MAAM,EAAE;YAAO,CAAE;YAAAZ,QAAA,EAAC;UAE9F;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEbrK,OAAA,CAACsB,OAAO;YAAAqJ,QAAA,EACL,CAAC,UAAU,EAAE,WAAW,CAAC,CAACa,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBAC1C1L,OAAA,CAACjC,GAAG;cAAayM,EAAE,EAAE;gBAAEmB,SAAS,EAAE;cAAS,CAAE;cAAAhB,QAAA,gBAC3C3K,OAAA,CAAC7B,UAAU;gBAAAwM,QAAA,EAAEc;cAAK;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChCrK,OAAA,CAAC7B,UAAU;gBAACiN,OAAO,EAAC,WAAW;gBAAAT,QAAA,EAC5B7G,UAAU,CAAC4H,KAAK,GAAG,UAAU,GAAG,SAAS,CAAC,GAAGhN,MAAM,CAACoF,UAAU,CAAC4H,KAAK,GAAG,UAAU,GAAG,SAAS,CAAC,CAAC,CAACE,MAAM,CAAC,OAAO,CAAC,GAAG;cAAG;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5G,CAAC;YAAA,GAJLoB,KAAK;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,EAEL,CAACvG,UAAU,CAACoF,QAAQ,iBACnBlJ,OAAA,CAACjC,GAAG;UAACyM,EAAE,EAAE;YAAEqB,EAAE,EAAE;UAAE,CAAE;UAAAlB,QAAA,EAEhB,CAAC7G,UAAU,CAACiE,OAAO,gBAClB/H,OAAA,CAAChC,MAAM;YACL8N,SAAS;YACTV,OAAO,EAAC,WAAW;YACnBW,OAAO,EAAExD,aAAc;YACvByD,QAAQ,EAAE1H,UAAW;YACrBkG,EAAE,EAAE;cACFyB,eAAe,EAAE3H,UAAU,GAAG,SAAS,GAAG,OAAO;cACjD4G,KAAK,EAAE,OAAO;cACdgB,UAAU,EAAE,MAAM;cAClBC,YAAY,EAAE,KAAK;cACnBC,KAAK,EAAE,OAAO;cACdrB,MAAM,EAAE,MAAM;cACdsB,SAAS,EAAE,gCAAgC;cAC3C9K,OAAO,EAAE,MAAM;cACfmJ,UAAU,EAAE,QAAQ;cACpB,SAAS,EAAE;gBAAEuB,eAAe,EAAE3H,UAAU,GAAG,SAAS,GAAG;cAAY;YACrE,CAAE;YAAAqG,QAAA,EAEDrG,UAAU,GAAG,eAAe,GAAG;UAAU;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,gBAETrK,OAAA;YACEsM,KAAK,EAAE;cACL/K,OAAO,EAAE,MAAM;cACfC,cAAc,EAAE,eAAe;cAC/BkJ,UAAU,EAAE,QAAQ;cACpB0B,KAAK,EAAE,MAAM;cACbG,SAAS,EAAE,MAAM;cACjBC,YAAY,EAAE,MAAM;cACpBC,GAAG,EAAE;YACP,CAAE;YAAA9B,QAAA,GAGD/G,aAAa,CAACe,MAAM,GAAG,CAAC,KAAA/C,gBAAA,GAAIgC,aAAa,CAAC,CAAC,CAAC,cAAAhC,gBAAA,eAAhBA,gBAAA,CAAkB8K,WAAW,gBACxD1M,OAAA,CAAC1B,WAAW;cAAAqM,QAAA,eACV3K,OAAA,CAAC3B,MAAM;gBACLsO,OAAO,EAAC,aAAa;gBACrB7C,KAAK,EAAElH,SAAU;gBACjB6I,KAAK,EAAC,YAAY;gBAClBmB,QAAQ,EAAEjD,cAAe;gBACzBa,EAAE,EAAE;kBACFyB,eAAe,EAAE,SAAS;kBAC1Bf,KAAK,EAAE,OAAO;kBACdgB,UAAU,EAAE,MAAM;kBAClBC,YAAY,EAAE,KAAK;kBACnBC,KAAK,EAAE,OAAO;kBACdrB,MAAM,EAAE,MAAM;kBACdsB,SAAS,EAAE,gCAAgC;kBAC3C9K,OAAO,EAAE,MAAM;kBACfmJ,UAAU,EAAE;gBACd,CAAE;gBACFmC,WAAW,EAAEA,CAAA,KAAM,MAAO;gBAAAlC,QAAA,eAE1B3K,OAAA,CAAC5B,QAAQ;kBAAC0L,KAAK,EAAC,UAAU;kBAAAa,QAAA,EAAC;gBAAS;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,gBAEdrK,OAAA,CAAC1B,WAAW;cAAAqM,QAAA,eACV3K,OAAA,CAAC3B,MAAM;gBACLsO,OAAO,EAAC,aAAa;gBACrB7C,KAAK,EAAElH,SAAS,IAAI,EAAG;gBACvB6I,KAAK,EAAC,YAAY;gBAClBmB,QAAQ,EAAEjD,cAAe;gBACzBa,EAAE,EAAE;kBACFyB,eAAe,EAAE,SAAS;kBAC1Bf,KAAK,EAAE,OAAO;kBACdgB,UAAU,EAAE,MAAM;kBAClBC,YAAY,EAAE,KAAK;kBACnBC,KAAK,EAAE,OAAO;kBACdrB,MAAM,EAAE,MAAM;kBACdsB,SAAS,EAAE,gCAAgC;kBAC3C9K,OAAO,EAAE,MAAM;kBACfmJ,UAAU,EAAE;gBACd,CAAE;gBACFmC,WAAW,EAAEA,CAAA,KAAM,UAAW;gBAAAlC,QAAA,gBAE9B3K,OAAA,CAAC5B,QAAQ;kBAAC0L,KAAK,EAAC,EAAE;kBAACkC,QAAQ;kBAAArB,QAAA,EAAC;gBAAQ;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC/CrK,OAAA,CAAC5B,QAAQ;kBAAC0L,KAAK,EAAC,YAAY;kBAAAa,QAAA,EAAC;gBAAW;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACnDrK,OAAA,CAAC5B,QAAQ;kBAAC0L,KAAK,EAAC,UAAU;kBAAAa,QAAA,EAAC;gBAAS;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC/CrK,OAAA,CAAC5B,QAAQ;kBAAC0L,KAAK,EAAC,OAAO;kBAAAa,QAAA,EAAC;gBAAK;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACd,eAGDrK,OAAA,CAAChC,MAAM;cACLoN,OAAO,EAAC,WAAW;cACnBF,KAAK,EAAC,OAAO;cACba,OAAO,EAAE/C,cAAe;cACxBgD,QAAQ,EAAExH,WAAY;cACtBgG,EAAE,EAAE;gBACFyB,eAAe,EAAEzH,WAAW,GAAG,SAAS,GAAG,QAAQ;gBACnD0G,KAAK,EAAE,OAAO;gBACdgB,UAAU,EAAE,MAAM;gBAClBC,YAAY,EAAE,KAAK;gBACnBC,KAAK,EAAE,OAAO;gBACdrB,MAAM,EAAE,MAAM;gBACdsB,SAAS,EAAE,gCAAgC;gBAC3C9K,OAAO,EAAE,MAAM;gBACfmJ,UAAU,EAAE;cACd,CAAE;cAAAC,QAAA,EAEDnG,WAAW,GAAG,eAAe,GAAG;YAAW;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,EAGArG,aAAa,IAAIJ,aAAa,CAACe,MAAM,KAAK,CAAC,IAAI,CAACqB,cAAc,CAACjD,OAAO,CAAC,mBAAmB,CAAC,iBACpG/C,OAAA,CAACT,SAAS;UACRuN,KAAK,EAAC,cAAc;UACpBC,IAAI,EAAC,MAAM;UACXC,QAAQ,EAAEvE,gBAAiB,CAAE;UAAA;UAC7BwE,OAAO,EAAEA,CAAA,KAAM;YACbhJ,gBAAgB,CAAC,KAAK,CAAC;YACvB+B,cAAc,CAACC,OAAO,CAAC,mBAAmB,EAAE,MAAM,CAAC;UACrD,CAAE;UACFiH,QAAQ,EAAE,IAAK,CAAE;QAAA;UAAAhD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CACF,EAGUrH,YAAY,iBACXhD,OAAA,CAACR,UAAU;UACT2N,OAAO,EAAE,IAAK;UACdC,UAAU,EAAErD,oBAAqB;UACjCnF,GAAG,EAAEhB,aAAa,CAAC,CAAC,CAAC,CAACgB,GAAI;UAC1B5C,OAAO,EAAEA;QAAQ;UAAAkI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CACF,EAEAnH,WAAW,iBACVlD,OAAA,CAACP,SAAS;UACR0N,OAAO,EAAE,IAAK;UACdJ,IAAI,EAAC,MAAM;UACXM,WAAW,EAAC,eAAe;UAC3BvI,EAAE,EAAElB,aAAa,CAAC,CAAC,CAAC,CAACgB,GAAI;UACzBqI,OAAO,EAAEA,CAAA,KAAM;YACbjH,cAAc,CAACC,OAAO,CAAC,0BAA0B,EAAE,MAAM,CAAC;YAC1D9C,cAAc,CAAC,KAAK,CAAC;UACvB;QAAE;UAAA+G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACF,EAEA/G,WAAW,iBACVtD,OAAA,CAACP,SAAS;UACR0N,OAAO,EAAE,IAAK;UACdJ,IAAI,EAAC,QAAQ;UACbM,WAAW,EAAC,aAAa;UACzBvI,EAAE,EAAElB,aAAa,CAAC,CAAC,CAAC,CAACgB,GAAI;UACzBqI,OAAO,EAAEA,CAAA,KAAM;YACbjH,cAAc,CAACC,OAAO,CAAC,0BAA0B,EAAE,MAAM,CAAC;YAC1D1C,cAAc,CAAC,KAAK,CAAC;UACvB;QAAE;UAAA2G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACF,EAEAjH,aAAa,iBACZpD,OAAA,CAACP,SAAS;UACR0N,OAAO,EAAE,IAAK;UACdJ,IAAI,EAAC,OAAO;UACZM,WAAW,EAAC,iBAAiB;UAC7BvI,EAAE,EAAElB,aAAa,CAAC,CAAC,CAAC,CAACgB,GAAI;UACzBqI,OAAO,EAAEA,CAAA,KAAM;YACbjH,cAAc,CAACC,OAAO,CAAC,4BAA4B,EAAE,MAAM,CAAC;YAC5D5C,gBAAgB,CAAC,KAAK,CAAC;UACzB;QAAE;UAAA6G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACF,EAEA7G,cAAc,iBACbxD,OAAA,CAACN,cAAc;UACbyN,OAAO,EAAE,IAAK;UACdrI,EAAE,EAAElB,aAAa,CAAC,CAAC,CAAC,CAACgB,GAAI;UACzBqI,OAAO,EAAEA,CAAA,KAAM;YACbjH,cAAc,CAACC,OAAO,CAAC,6BAA6B,EAAE,MAAM,CAAC;YAC7DxC,iBAAiB,CAAC,KAAK,CAAC;UAC1B;QAAE;UAAAyG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACF,eAGDrK,OAAA,CAACL,eAAe;UACd2N,IAAI,EAAElJ,mBAAoB;UAC1BmJ,UAAU,EAAErJ,eAAgB;UAC5B+I,OAAO,EAAEA,CAAA,KAAM5I,sBAAsB,CAAC,KAAK;QAAE;UAAA6F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPrK,OAAA,CAAC9B,IAAI;MAAC0M,IAAI;MAACC,EAAE,EAAE,EAAG;MAAAF,QAAA,eAChB3K,OAAA,CAAC9B,IAAI;QAACoM,SAAS;QAACC,OAAO,EAAE,CAAE;QAACC,EAAE,EAAE;UAAEjJ,OAAO,EAAE,MAAM;UAAEmJ,UAAU,EAAE;QAAS,CAAE;QAAAC,QAAA,gBAExE3K,OAAA,CAAC9B,IAAI;UAAC0M,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAH,QAAA,eACvB3K,OAAA,CAACH,kBAAkB;YAACsC,UAAU,EAAEA;UAAW;YAAA+H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,EAGNzM,GAAG,CAACC,OAAO,CAACoM,QAAQ,EAAEnM,QAAQ,CAAC0P,KAAK,CAAC,iBACpCxN,OAAA,CAAC9B,IAAI;UAAC0M,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACR,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAI,QAAA,gBAC7C3K,OAAA,CAAC9B,IAAI;YAAC0M,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eAChB3K,OAAA,CAACF,MAAM;cACLgN,KAAK,EAAC,aAAa;cACnBW,OAAO,EAAEpL,UAAU,aAAVA,UAAU,cAAVA,UAAU,GAAI,CAAE;cACzBqL,IAAI,eAAE1N,OAAA,CAACvB,qBAAqB;gBAAC+L,EAAE,EAAE;kBAAEU,KAAK,EAAEpJ,KAAK,CAAC6L,OAAO,CAACC,OAAO,CAACC,IAAI;kBAAE1C,QAAQ,EAAE;gBAAG;cAAE;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EACNzM,GAAG,CAACC,OAAO,CAACmJ,IAAI,EAAElJ,QAAQ,CAACgG,UAAU,CAAC,iBACrC9D,OAAA,CAAC9B,IAAI;YAAC0M,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eAChB3K,OAAA,CAACF,MAAM;cACLgN,KAAK,EAAC,aAAa;cACnBW,OAAO,GAAA5L,mBAAA,GAAES,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEwL,UAAU,cAAAjM,mBAAA,cAAAA,mBAAA,GAAI,CAAE;cAClC6L,IAAI,eAAE1N,OAAA,CAACvB,qBAAqB;gBAAC+L,EAAE,EAAE;kBAAEU,KAAK,EAAEpJ,KAAK,CAAC6L,OAAO,CAACC,OAAO,CAACC,IAAI;kBAAE1C,QAAQ,EAAE;gBAAG;cAAE;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAGX;AAAC1I,EAAA,CAzuBuBD,QAAQ;EAAA,QAChBpC,QAAQ,EACLR,WAAW,EACZC,WAAW,EACPA,WAAW,EACZA,WAAW,EACXA,WAAW,EACdA,WAAW,EAubHA,WAAW;AAAA;AAAAgP,GAAA,GA9bbrM,QAAQ;AAAA,IAAAD,EAAA,EAAAsM,GAAA;AAAAC,YAAA,CAAAvM,EAAA;AAAAuM,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}